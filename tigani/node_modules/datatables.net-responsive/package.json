{"name": "datatables.net-responsive", "description": "Responsive for DataTables", "main": "js/dataTables.responsive.js", "module": "js/dataTables.responsive.mjs", "types": "./types/types.d.ts", "version": "3.0.6", "files": ["js/**/*.js", "js/**/*.mjs", "types/**/*.d.ts"], "keywords": ["Responsive", "Datatables", "j<PERSON><PERSON><PERSON>", "table", "filter", "sort"], "dependencies": {"datatables.net": "^2", "jquery": ">=1.7"}, "moduleType": ["globals", "amd", "node"], "ignore": ["composer.json", "datatables.json", "package.json"], "author": {"name": "SpryMedia Ltd", "url": "http://datatables.net"}, "homepage": "https://datatables.net", "bugs": "https://datatables.net/forums", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/DataTables/Dist-DataTables-Responsive.git"}}