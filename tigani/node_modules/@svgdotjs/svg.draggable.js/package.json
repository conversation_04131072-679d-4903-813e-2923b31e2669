{"name": "@svgdotjs/svg.draggable.js", "version": "3.0.6", "description": "An extension for svg.js which allows to drag elements with your mouse", "type": "module", "main": "dist/svg.draggable.js", "module": "src/svg.draggable.js", "exports": {".": {"import": {"types": "./svg.draggable.js.d.ts", "default": "./src/svg.draggable.js"}, "require": {"types": "./svg.draggable.js.d.cts", "default": "./src/svg.draggable.js"}, "browser": {"types": "./svg.draggable.js.d.ts", "default": "./src/svg.draggable.js"}}}, "unpkg": "dist/svg.draggable.js", "jsdelivr": "dist/svg.draggable.js", "files": ["/dist", "/src", "/svg.draggable.js.d.ts", "/svg.draggable.js.d.cts"], "keywords": ["svg.js", "draggable", "mouse"], "bugs": "https://github.com/svgdotjs/svg.draggable.js/issues", "license": "MIT", "typings": "./svg.draggable.js.d.ts", "author": {"name": "Wout <PERSON>"}, "contributors": [{"name": "Wout <PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/svgdotjs/svg.draggable.js", "repository": {"type": "git", "url": "git+https://github.com/svgdotjs/svg.draggable.js.git"}, "scripts": {"build": "npm run fix && vite build", "fix": "npx eslint --fix", "prepublishOnly": "rm -rf ./dist && npm run build"}, "devDependencies": {"eslint": "^8.36.0", "eslint-plugin-prettier": "^4.2.1", "eslint-config-prettier": "^8.8.0", "prettier": "^2.8.5", "typescript": "^5.0.2", "vite": "^4.2.1"}, "peerDependencies": {"@svgdotjs/svg.js": "^3.2.4"}}