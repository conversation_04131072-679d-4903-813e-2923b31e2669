{"name": "@svgdotjs/svg.resize.js", "version": "2.0.5", "description": "An extension for svg.js which allows to resize elements which are selected", "type": "module", "keywords": ["svg.js", "resize", "mouse"], "bugs": "https://github.com/svgdotjs/svg.resize.js/issues", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>"}, "homepage": "https://github.com/svgdotjs/svg.resize.js", "main": "dist/svg.resize.umd.cjs", "unpkg": "dist/svg.resize.iife.js", "jsdelivr": "dist/svg.resize.iife.js", "module": "dist/svg.resize.js", "typings": "./svg.resize.js.d.ts", "exports": {".": {"import": {"types": "./svg.resize.js.d.ts", "default": "./dist/svg.resize.js"}, "require": {"types": "./svg.resize.js.d.cts", "default": "./dist/svg.resize.umd.cjs"}, "browser": {"types": "./svg.resize.js.d.ts", "default": "./dist/svg.resize.js"}}}, "files": ["/dist", "/src", "/svg.resize.js.d.ts"], "scripts": {"dev": "vite", "build": "tsc && prettier --write . && eslint ./src && vite build", "zip": "zip -j dist/svg.resize.js.zip -- LICENSE README.md dist/svg.resize.iife.js  dist/svg.resize.iife.js.map  dist/svg.resize.js  dist/svg.resize.js.map  dist/svg.resize.umd.cjs  dist/svg.resize.umd.cjs.map", "prepublishOnly": "rm -rf ./dist && npm run build", "postpublish": "npm run zip"}, "repository": {"type": "git", "url": "git+https://github.com/svgdotjs/svg.resize.js.git"}, "engines": {"node": ">= 14.18"}, "devDependencies": {"@types/node": "^20.14.10", "eslint": "^9.7.0", "eslint-plugin-import-x": "^3.0.1", "prettier": "^3.3.3", "terser": "^5.31.2", "typescript": "^5.5.3", "vite": "^5.3.4"}, "peerDependencies": {"@svgdotjs/svg.js": "^3.2.4", "@svgdotjs/svg.select.js": "^4.0.1"}}