package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class RuleFieldController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleFieldController.class.getName());

    public static TemplateViewRoute be_rulefield_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.be_rulefield_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_rulefield = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("ruleFieldId"));
        if (oid != null) {
            RuleFieldDescriptor loadedRuleField = BaseDao.getDocumentById(oid, RuleFieldDescriptor.class);
            attributes.put("curRuleField", loadedRuleField);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                RuleFieldDescriptor loadedRuleField = BaseDao.getDocumentByParentId(parentId, RuleFieldDescriptor.class);
                if (loadedRuleField != null) {
                    attributes.put("curRuleField", loadedRuleField);
                }
            }
        }

        return Core.render(Pages.BE_RULEFIELD, attributes, request);
    };

    public static TemplateViewRoute be_rulefield_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("ruleFieldId"));
        if (oid != null) {
            RuleFieldDescriptor loadedRuleField = BaseDao.getDocumentById(oid, RuleFieldDescriptor.class);
            attributes.put("curRuleField", loadedRuleField);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                RuleFieldDescriptor loadedRuleField = BaseDao.getDocumentByParentId(parentId, RuleFieldDescriptor.class);
                if (loadedRuleField != null) {
                    attributes.put("curRuleField", loadedRuleField);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.be_rulefield_FORM, attributes, request);
    };

    public static Route be_rulefield_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<RuleFieldDescriptor> loadedRuleFields;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedRuleFields = BaseDao.getDocumentsByFilters(RuleFieldDescriptor.class, queryOptions, loadArchived);
        } else {
            loadedRuleFields = BaseDao.getDocumentsByFilters(RuleFieldDescriptor.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedRuleFields.isEmpty()) {
            for (RuleFieldDescriptor tmpRuleFieldDescriptor : loadedRuleFields) {
                List<String> row = new ArrayList<>();
                row.add(tmpRuleFieldDescriptor.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' ruleFieldId='" +
                    tmpRuleFieldDescriptor.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpRuleFieldDescriptor.getDescription(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpRuleFieldDescriptor.getCode(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpRuleFieldDescriptor.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpRuleFieldDescriptor.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_rulefield_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("ruleFieldId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), requiredPermission);

        RuleFieldDescriptor newRuleField;
        if (oid != null) {
            newRuleField = BaseDao.getDocumentById(oid, RuleFieldDescriptor.class);
            RequestUtils.mergeFromParams(params, newRuleField);
        } else {
            newRuleField = RequestUtils.createFromParams(params, RuleFieldDescriptor.class);
        }

        if (newRuleField != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newRuleField);
                newRuleField.setId(oid);

                BaseDao.insertLog(user, newRuleField, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newRuleField, user);
                BaseDao.insertLog(user, newRuleField, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_rulefield_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.RULEFIELD_MANAGEMENT.getCode(), requiredPermission);

        String ruleFieldIds = params.get("ruleFieldIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(ruleFieldIds)) {
            String[] ids = ruleFieldIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    RuleFieldDescriptor tmpRuleFieldDescriptor = BaseDao.getDocumentById(oid, RuleFieldDescriptor.class);
                    if (tmpRuleFieldDescriptor != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpRuleFieldDescriptor, user);
                                BaseDao.insertLog(user, tmpRuleFieldDescriptor, LogType.DELETE);
                                break;
                            case "archive":
                                tmpRuleFieldDescriptor.setArchived(true);
                                BaseDao.updateDocument(tmpRuleFieldDescriptor, user);
                                BaseDao.insertLog(user, tmpRuleFieldDescriptor, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpRuleFieldDescriptor.setArchived(false);
                                BaseDao.updateDocument(tmpRuleFieldDescriptor, user);
                                BaseDao.insertLog(user, tmpRuleFieldDescriptor, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
