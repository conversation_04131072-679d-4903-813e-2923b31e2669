package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Mailtemplate;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.Defaults;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class MailtemplateController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailtemplateController.class.getName());

    public static TemplateViewRoute be_mailtemplate_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_MAILTEMPLATE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_mailtemplate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("mailtemplateId"));
        if (oid != null) {
            Mailtemplate loadedMailtemplate = BaseDao.getDocumentById(oid, Mailtemplate.class, language);
            attributes.put("mailtemplate", loadedMailtemplate);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Mailtemplate loadedMailtemplate = BaseDao.getDocumentByParentId(parentId, Mailtemplate.class, language);
                if (loadedMailtemplate != null) {
                    attributes.put("mailtemplate", loadedMailtemplate);
                }
            }
        }

        return Core.render(Pages.BE_MAILTEMPLATE, attributes, request);
    };

    public static Route be_mailtemplate_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Mailtemplate> mailtemplateListToCheck = new ArrayList<>();
        Map<String, Mailtemplate> parentMailtemplateMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Mailtemplate> loadedMailtemplates;
            if (loadArchived) {
                loadedMailtemplates = BaseDao.getArchivedDocuments(Mailtemplate.class, languageToLoad);
            } else {
                loadedMailtemplates = BaseDao.getDocuments(Mailtemplate.class, languageToLoad);
            }
            if (loadedMailtemplates != null && !loadedMailtemplates.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Mailtemplate mailtemplate : loadedMailtemplates) {
                        parentMailtemplateMap.put(mailtemplate.getParentId(), mailtemplate);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    mailtemplateListToCheck.addAll(loadedMailtemplates);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!mailtemplateListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Mailtemplate mailtemplate : mailtemplateListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, mailtemplate.getLanguage())) {
                            if (!parentMailtemplateMap.containsKey(mailtemplate.getParentId())) {
                                parentMailtemplateMap.put(mailtemplate.getParentId(), mailtemplate);
                            }
                        }
                    }
                }
            }
        }
        List<Mailtemplate> mailtemplateList = new ArrayList<>(parentMailtemplateMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!mailtemplateList.isEmpty()) {
            for (Mailtemplate mailtemplate : mailtemplateList) {
                json.append("[");
                json.append("\"<a language='").append(mailtemplate.getLanguage()).append("' mailtemplateId='").append(mailtemplate.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_MAILTEMPLATE + "?mailtemplateId=").append(mailtemplate.getId()).append("&language=").append(mailtemplate.getLanguage()).append("'>").append(mailtemplate.getKey()).append("</a>\",");
                json.append("\"").append(StringUtils.join(mailtemplate.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(mailtemplate.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_mailtemplate_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("mailtemplateId"));
        Mailtemplate newMailtemplate;
        if (oid != null) {
            newMailtemplate = BaseDao.getDocumentById(oid, Mailtemplate.class, language);
            RequestUtils.mergeFromParams(params, newMailtemplate);
        } else {
            newMailtemplate = RequestUtils.createFromParams(params, Mailtemplate.class);
        }

        if (newMailtemplate != null) {
            Mailtemplate loadedByKey = BaseDao.getDocumentByKey(newMailtemplate.getKey(), Mailtemplate.class, language);
            Mailtemplate loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Mailtemplate.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Mailtemplate mailtemplateToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Mailtemplate.class, languageToUpdate);
                        if (mailtemplateToUpdate != null) {
                            mailtemplateToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(mailtemplateToUpdate, mailtemplateToUpdate.getLanguage(), user);
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage(), user);
            }

            if (loadedByKey != null && loadedByKey.getId() != null) {
                if (newMailtemplate.getId() == null || !loadedByKey.getId().equals(newMailtemplate.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Mailtemplate with the same key already exists");
                }
            }

            newMailtemplate.setLanguage(language);
            if (loadedByParentId != null) {
                newMailtemplate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newMailtemplate.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newMailtemplate.getParentId())) {
                newMailtemplate.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newMailtemplate.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newMailtemplate, language);
                newMailtemplate.setId(oid);

                BaseDao.insertLog(user, newMailtemplate, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newMailtemplate, language, user);
                BaseDao.insertLog(user, newMailtemplate, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(mailtemplateFromQuery, newMailtemplate);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newMailtemplate, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newMailtemplate, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newMailtemplate != null ? newMailtemplate.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_mailtemplate_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String mailtemplates = params.get("mailtemplateIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(mailtemplates) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> mailtemplateIds = new HashMap<>();
            if (StringUtils.contains(mailtemplates, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(mailtemplates, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        mailtemplateIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(mailtemplates, "|"));
                if (parts.size() == 2) {
                    mailtemplateIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!mailtemplateIds.isEmpty()) {
                for (ObjectId mailtemplateId : mailtemplateIds.keySet()) {
                    String language = mailtemplateIds.get(mailtemplateId);
                    Mailtemplate mailtemplateToArchive;
                    if (isArchived) {
                        mailtemplateToArchive = BaseDao.getArchivedDocumentById(mailtemplateId, Mailtemplate.class, language);
                    } else {
                        mailtemplateToArchive = BaseDao.getDocumentById(mailtemplateId, Mailtemplate.class, language);
                    }
                    if (mailtemplateToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            mailtemplateToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            mailtemplateToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(mailtemplateToArchive, language, user);

                        List<String> languagesToUpdate = mailtemplateToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Mailtemplate subMailtemplateToArchive = BaseDao.getDocumentByParentId(mailtemplateToArchive.getParentId(), Mailtemplate.class, languageToUpdate);
                                    if (subMailtemplateToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subMailtemplateToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subMailtemplateToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subMailtemplateToArchive, languageToUpdate, user);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
