package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.WarrantyDao;
import enums.CriteriaType;
import enums.ProfileType;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.InsuranceProvenanceType;
import pojo.Province;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.RequestUtils;

import java.util.*;

/**
 * Controller for warranty criteria testing functionality
 * 
 * <AUTHOR>
 */
public class WarrantyCriteriaTestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyCriteriaTestController.class.getName());

    public static TemplateViewRoute be_warranty_criteria_test = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // Add CriteriaType enum for form generation
        attributes.put("criteriaTypes", CriteriaType.values());

        return Core.render(Pages.BE_WARRANTY_CRITERIA_TEST, attributes, request);
    };

    public static Route be_warranty_criteria_search = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        try {
            // Build criteria values from request parameters
            Map<String, Object> criteriaValues = new HashMap<>();
            
            // Province code
            String provinceCode = params.get("provinceCode");
            if (StringUtils.isNotBlank(provinceCode)) {
                criteriaValues.put("provinceCode", provinceCode);
            }
            
            // Claim number
            String claimNumberStr = params.get("claimNumber");
            if (StringUtils.isNotBlank(claimNumberStr)) {
                try {
                    Integer claimNumber = Integer.parseInt(claimNumberStr);
                    criteriaValues.put("claimNumber", claimNumber);
                } catch (NumberFormatException e) {
                    LOGGER.warn("Invalid claim number format: " + claimNumberStr);
                }
            }
            
            // Insurance provenance type
            String insuranceProvenanceTypeId = params.get("insuranceProvenanceTypeId");
            if (StringUtils.isNotBlank(insuranceProvenanceTypeId)) {
                try {
                    ObjectId oid = new ObjectId(insuranceProvenanceTypeId);
                    criteriaValues.put("insuranceProvenanceTypeId", oid);
                } catch (IllegalArgumentException e) {
                    LOGGER.warn("Invalid insurance provenance type ID format: " + insuranceProvenanceTypeId);
                }
            }
            
            // Universal class
            String universalClassStr = params.get("universalClass");
            if (StringUtils.isNotBlank(universalClassStr)) {
                try {
                    Integer universalClass = Integer.parseInt(universalClassStr);
                    criteriaValues.put("universalClass", universalClass);
                } catch (NumberFormatException e) {
                    LOGGER.warn("Invalid universal class format: " + universalClassStr);
                }
            }

            // Search for matching warranties
            List<Map<String, Object>> results = WarrantyDao.findWarrantiesByCriteria(criteriaValues);
            
            // Build response JSON
            StringBuilder json = new StringBuilder();
            json.append("{");
            json.append("\"success\": true,");
            json.append("\"criteriaValues\": ").append(Core.serializeToJson(criteriaValues)).append(",");
            json.append("\"totalResults\": ").append(results.size()).append(",");
            json.append("\"results\": [");
            
            for (int i = 0; i < results.size(); i++) {
                Map<String, Object> result = results.get(i);
                
                if (i > 0) {
                    json.append(",");
                }
                
                json.append(Core.serializeToJson(result));
            }
            
            json.append("]}");
            
            response.type("application/json");
            return json.toString();
            
        } catch (Exception e) {
            LOGGER.error("Error searching warranties by criteria", e);
            response.status(500);
            response.type("application/json");
            return "{ \"success\": false, \"error\": \"" + e.getMessage() + "\" }";
        }
    };
}
