package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class ModelController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelController.class.getName());

    public static TemplateViewRoute be_model_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_MODEL_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_model = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelId"));
        if (oid != null) {
            Model loadedModel = BaseDao.getDocumentById(oid, Model.class);
            attributes.put("curModel", loadedModel);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Model loadedModel = BaseDao.getDocumentByParentId(parentId, Model.class);
                if (loadedModel != null) {
                    attributes.put("curModel", loadedModel);
                }
            }
        }

        return Core.render(Pages.BE_MODEL, attributes, request);
    };

    public static TemplateViewRoute be_model_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelId"));
        if (oid != null) {
            Model loadedModel = BaseDao.getDocumentById(oid, Model.class);
            attributes.put("curModel", loadedModel);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Model loadedModel = BaseDao.getDocumentByParentId(parentId, Model.class);
                if (loadedModel != null) {
                    attributes.put("curModel", loadedModel);
                }
            }
        }

        // Load brands for dropdown selection
        List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);
        attributes.put("brands", brands);

        // Return only the form content without the base template
        return Core.render(Pages.BE_MODEL_FORM, attributes, request);
    };

    public static Route be_model_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Model> loadedModels;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedModels = BaseDao.getDocumentsByFilters(Model.class, queryOptions, loadArchived);
        } else {
            loadedModels = BaseDao.getDocumentsByFilters(Model.class, queryOptions);
        }

        // Load brands for display purposes
        Map<ObjectId, Brand> brandMap = new HashMap<>();
        List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);
        for (Brand brand : brands) {
            brandMap.put(brand.getId(), brand);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedModels.isEmpty()) {
            for (Model tmpModel : loadedModels) {
                List<String> row = new ArrayList<>();
                row.add(tmpModel.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' modelId='" +
                    tmpModel.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpModel.getDescrizione(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpModel.getCodice(), "N.D."));

                // Brand name
                String brandName = "N.D.";
                if (tmpModel.getBrandId() != null && brandMap.containsKey(tmpModel.getBrandId())) {
                    brandName = brandMap.get(tmpModel.getBrandId()).getDescrizione();
                }
                row.add(brandName);

                row.add(DateTimeUtils.dateToString(tmpModel.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpModel.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_model_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), requiredPermission);

        Model newModel;
        if (oid != null) {
            newModel = BaseDao.getDocumentById(oid, Model.class);
            RequestUtils.mergeFromParams(params, newModel);
        } else {
            newModel = RequestUtils.createFromParams(params, Model.class);
        }

        if (newModel != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newModel);
                newModel.setId(oid);

                BaseDao.insertLog(user, newModel, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newModel, user);
                BaseDao.insertLog(user, newModel, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_model_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODEL_MANAGEMENT.getCode(), requiredPermission);

        String modelIds = params.get("modelIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(modelIds)) {
            String[] ids = modelIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Model tmpModel = BaseDao.getDocumentById(oid, Model.class);
                    if (tmpModel != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpModel, user);
                                BaseDao.insertLog(user, tmpModel, LogType.DELETE);
                                break;
                            case "archive":
                                tmpModel.setArchived(true);
                                BaseDao.updateDocument(tmpModel, user);
                                BaseDao.insertLog(user, tmpModel, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpModel.setArchived(false);
                                BaseDao.updateDocument(tmpModel, user);
                                BaseDao.insertLog(user, tmpModel, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
