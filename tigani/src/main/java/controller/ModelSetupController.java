package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class ModelSetupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelSetupController.class.getName());

    public static TemplateViewRoute be_modelsetup_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_MODELSETUP_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_modelsetup = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelSetupId"));
        if (oid != null) {
            ModelSetup loadedModelSetup = BaseDao.getDocumentById(oid, ModelSetup.class);
            attributes.put("curModelSetup", loadedModelSetup);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                ModelSetup loadedModelSetup = BaseDao.getDocumentByParentId(parentId, ModelSetup.class);
                if (loadedModelSetup != null) {
                    attributes.put("curModelSetup", loadedModelSetup);
                }
            }
        }

        return Core.render(Pages.BE_MODELSETUP, attributes, request);
    };

    public static TemplateViewRoute be_modelsetup_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelSetupId"));
        if (oid != null) {
            ModelSetup loadedModelSetup = BaseDao.getDocumentById(oid, ModelSetup.class);
            attributes.put("curModelSetup", loadedModelSetup);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                ModelSetup loadedModelSetup = BaseDao.getDocumentByParentId(parentId, ModelSetup.class);
                if (loadedModelSetup != null) {
                    attributes.put("curModelSetup", loadedModelSetup);
                }
            }
        }

        // Load brands and models for dropdown selection
        List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);
        attributes.put("brands", brands);

        List<Model> models = BaseDao.getDocumentsByClass(Model.class);
        attributes.put("models", models);

        // Return only the form content without the base template
        return Core.render(Pages.BE_MODELSETUP_FORM, attributes, request);
    };

    public static Route be_modelsetup_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<ModelSetup> loadedModelSetups;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedModelSetups = BaseDao.getDocumentsByFilters(ModelSetup.class, queryOptions, loadArchived);
        } else {
            loadedModelSetups = BaseDao.getDocumentsByFilters(ModelSetup.class, queryOptions);
        }

        // Load brands and models for display purposes
        Map<ObjectId, Brand> brandMap = new HashMap<>();
        List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);
        for (Brand brand : brands) {
            brandMap.put(brand.getId(), brand);
        }

        Map<ObjectId, Model> modelMap = new HashMap<>();
        List<Model> models = BaseDao.getDocumentsByClass(Model.class);
        for (Model model : models) {
            modelMap.put(model.getId(), model);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedModelSetups.isEmpty()) {
            for (ModelSetup tmpModelSetup : loadedModelSetups) {
                List<String> row = new ArrayList<>();
                row.add(tmpModelSetup.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' modelSetupId='" +
                    tmpModelSetup.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpModelSetup.getDescrizione(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpModelSetup.getCodice(), "N.D."));

                // Brand name
                String brandName = "N.D.";
                if (tmpModelSetup.getBrandId() != null && brandMap.containsKey(tmpModelSetup.getBrandId())) {
                    brandName = brandMap.get(tmpModelSetup.getBrandId()).getDescrizione();
                }
                row.add(brandName);

                // Model name
                String modelName = "N.D.";
                if (tmpModelSetup.getModelId() != null && modelMap.containsKey(tmpModelSetup.getModelId())) {
                    modelName = modelMap.get(tmpModelSetup.getModelId()).getDescrizione();
                }
                row.add(modelName);

                // Additional fields specific to ModelSetup
                row.add(tmpModelSetup.getAnnoImmatricolazione() != null ? tmpModelSetup.getAnnoImmatricolazione().toString() : "N.D.");
                row.add(tmpModelSetup.getMeseImmatricolazione() != null ? tmpModelSetup.getMeseImmatricolazione().toString() : "N.D.");
                row.add(tmpModelSetup.getValoreAssicurato() != null ? tmpModelSetup.getValoreAssicurato().toString() : "N.D.");

                row.add(DateTimeUtils.dateToString(tmpModelSetup.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpModelSetup.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_modelsetup_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("modelSetupId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), requiredPermission);

        ModelSetup newModelSetup;
        if (oid != null) {
            newModelSetup = BaseDao.getDocumentById(oid, ModelSetup.class);
            RequestUtils.mergeFromParams(params, newModelSetup);
        } else {
            newModelSetup = RequestUtils.createFromParams(params, ModelSetup.class);
        }

        if (newModelSetup != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newModelSetup);
                newModelSetup.setId(oid);

                BaseDao.insertLog(user, newModelSetup, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newModelSetup, user);
                BaseDao.insertLog(user, newModelSetup, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_modelsetup_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MODELSETUP_MANAGEMENT.getCode(), requiredPermission);

        String modelSetupIds = params.get("modelSetupIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(modelSetupIds)) {
            String[] ids = modelSetupIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    ModelSetup tmpModelSetup = BaseDao.getDocumentById(oid, ModelSetup.class);
                    if (tmpModelSetup != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpModelSetup, user);
                                BaseDao.insertLog(user, tmpModelSetup, LogType.DELETE);
                                break;
                            case "archive":
                                tmpModelSetup.setArchived(true);
                                BaseDao.updateDocument(tmpModelSetup, user);
                                BaseDao.insertLog(user, tmpModelSetup, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpModelSetup.setArchived(false);
                                BaseDao.updateDocument(tmpModelSetup, user);
                                BaseDao.insertLog(user, tmpModelSetup, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
