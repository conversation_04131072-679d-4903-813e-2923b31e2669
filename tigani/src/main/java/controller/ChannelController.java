package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Channel;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class ChannelController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChannelController.class.getName());

    public static TemplateViewRoute be_channel_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_CHANNEL_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_channel = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("channelId"));
        if (oid != null) {
            Channel loadedChannel = BaseDao.getDocumentById(oid, Channel.class);
            attributes.put("curChannel", loadedChannel);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Channel loadedChannel = BaseDao.getDocumentByParentId(parentId, Channel.class);
                if (loadedChannel != null) {
                    attributes.put("curChannel", loadedChannel);
                }
            }
        }

        return Core.render(Pages.BE_CHANNEL, attributes, request);
    };

    public static Route be_channel_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Channel> loadedChannels;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedChannels = BaseDao.getDocumentsByFilters(Channel.class, queryOptions, loadArchived);
        } else {
            loadedChannels = BaseDao.getDocumentsByFilters(Channel.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedChannels.isEmpty()) {
            for (Channel tmpChannel : loadedChannels) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' channelId='").append(tmpChannel.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_CHANNEL).append("?channelId=").append(tmpChannel.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpChannel.getCode(), "N.D.")).append("</a>\",");
                json.append("\"").append(tmpChannel.getCapacityLimit() != null ? tmpChannel.getCapacityLimit().toString() : "N.D.").append("\",");
                json.append("\"").append(tmpChannel.getBrandModelIds() != null ? tmpChannel.getBrandModelIds().size() : "0").append("\",");
                json.append("\"").append(tmpChannel.getWarrantyIds() != null ? tmpChannel.getWarrantyIds().size() : "0").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpChannel.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpChannel.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_channel_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("channelId"));
        Channel newChannel;
        if (oid != null) {
            newChannel = BaseDao.getDocumentById(oid, Channel.class);
            RequestUtils.mergeFromParams(params, newChannel);
        } else {
            newChannel = RequestUtils.createFromParams(params, Channel.class);
        }

        if (newChannel != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newChannel);
                newChannel.setId(oid);

                BaseDao.insertLog(user, newChannel, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newChannel, user);
                BaseDao.insertLog(user, newChannel, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newChannel, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newChannel, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_channel_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String channelIds = params.get("channelIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(channelIds)) {
            String[] ids = channelIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Channel tmpChannel = BaseDao.getDocumentById(oid, Channel.class);
                    if (tmpChannel != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpChannel, user);
                                BaseDao.insertLog(user, tmpChannel, LogType.DELETE);
                                break;
                            case "archive":
                                tmpChannel.setArchived(true);
                                BaseDao.updateDocument(tmpChannel, user);
                                BaseDao.insertLog(user, tmpChannel, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpChannel.setArchived(false);
                                BaseDao.updateDocument(tmpChannel, user);
                                BaseDao.insertLog(user, tmpChannel, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
