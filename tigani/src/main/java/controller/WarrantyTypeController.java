package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.WarrantyType;
import pojo.User;
import pojo.RoutesPermission;
import spark.*;
import utils.*;

import java.util.*;
import java.util.Date;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class WarrantyTypeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyTypeController.class.getName());

    public static TemplateViewRoute be_warrantytype_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_WARRANTYTYPE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_warrantytype = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyTypeId"));
        if (oid != null) {
            WarrantyType loadedWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
            attributes.put("curWarrantyType", loadedWarrantyType);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                WarrantyType loadedWarrantyType = BaseDao.getDocumentByParentId(parentId, WarrantyType.class);
                if (loadedWarrantyType != null) {
                    attributes.put("curWarrantyType", loadedWarrantyType);
                }
            }
        }

        return Core.render(Pages.BE_WARRANTYTYPE, attributes, request);
    };

    public static TemplateViewRoute be_warrantytype_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyTypeId"));
        if (oid != null) {
            WarrantyType loadedWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
            attributes.put("curWarrantyType", loadedWarrantyType);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                WarrantyType loadedWarrantyType = BaseDao.getDocumentByParentId(parentId, WarrantyType.class);
                if (loadedWarrantyType != null) {
                    attributes.put("curWarrantyType", loadedWarrantyType);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_WARRANTYTYPE_FORM, attributes, request);
    };

    public static Route be_warrantytype_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<WarrantyType> loadedWarrantyTypes;
        List<Bson> filters = new ArrayList<>();

        // Date filters
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedWarrantyTypes = BaseDao.getDocumentsByFilters(WarrantyType.class, queryOptions, loadArchived);
        } else {
            loadedWarrantyTypes = BaseDao.getDocumentsByFilters(WarrantyType.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        for (WarrantyType warrantyType : loadedWarrantyTypes) {
            List<String> row = new ArrayList<>();
            row.add(warrantyType.getId().toString());
            // Code con link
            String codeLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' warrantyTypeId='" +
                warrantyType.getId() + "'>" +
                StringUtils.defaultIfBlank(warrantyType.getCode(), "N.D.") + "</a>";
            row.add(codeLink);
            row.add(StringUtils.defaultIfBlank(warrantyType.getName(), ""));
            String icon = "";
            if (StringUtils.isNotBlank(warrantyType.getIcon())) {
                icon = "<img src='" + RoutesUtils.contextPath(request) + "/img/warranties/" + warrantyType.getIcon() + ".svg' alt='" + warrantyType.getIcon() + "'>";
            }
            row.add(icon);
            row.add(DateTimeUtils.dateToString(warrantyType.getCreation(), "dd/MM/yyyy"));
            row.add(DateTimeUtils.dateToString(warrantyType.getLastUpdate(), "dd/MM/yyyy"));
            row.add(String.valueOf(warrantyType.getArchived() != null ? warrantyType.getArchived() : false));
            dataRows.add(row);
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_warrantytype_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyTypeId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), requiredPermission);

        WarrantyType newWarrantyType;
        if (oid != null) {
            newWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
            RequestUtils.mergeFromParams(params, newWarrantyType);
        } else {
            newWarrantyType = RequestUtils.createFromParams(params, WarrantyType.class);
        }

        if (newWarrantyType != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newWarrantyType);
                newWarrantyType.setId(oid);

                BaseDao.insertLog(user, newWarrantyType, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newWarrantyType, user);
                BaseDao.insertLog(user, newWarrantyType, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_warrantytype_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_TYPE_MANAGEMENT.getCode(), requiredPermission);

        String warrantyTypeIds = params.get("warrantyTypeIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(warrantyTypeIds)) {
            String[] ids = warrantyTypeIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    WarrantyType tmpWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
                    if (tmpWarrantyType != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpWarrantyType, user);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.DELETE);
                                break;
                            case "archive":
                                tmpWarrantyType.setArchived(true);
                                BaseDao.updateDocument(tmpWarrantyType, user);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpWarrantyType.setArchived(false);
                                BaseDao.updateDocument(tmpWarrantyType, user);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
