package enums;

import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public enum ProfileType {

    SYSTEM(4),
    ADMIN(3),
    OPERATOR(2),
    CUSTOMER(1),
    UNCONFIRMED(0);
    
    private final int value;

    ProfileType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
    
    public static int getValueForRole(String roleName) {
        for (ProfileType role : ProfileType.values()) {
            if (StringUtils.equalsIgnoreCase(role.name(), roleName)) {
                return role.getValue();
            }
        }
        return Integer.MIN_VALUE; // Ruolo non trovato, restituisci un valore minimo
    }    
}
