package commons;

import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Brand;
import pojo.Model;
import pojo.ModelSetup;
import pojo.QueryOptions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Cache utility class for storing frequently accessed motorcycle data in memory
 * Provides fast access to Brand, Model, and ModelSetup data without database queries
 * 
 * <AUTHOR>
 */
public class CacheCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheCommons.class.getName());

    // Thread-safe maps for caching data
    private static final Map<ObjectId, Brand> brandCache = new ConcurrentHashMap<>();
    private static final Map<String, Brand> brandByCodeCache = new ConcurrentHashMap<>();
    
    private static final Map<ObjectId, Model> modelCache = new ConcurrentHashMap<>();
    private static final Map<String, Model> modelByCodeCache = new ConcurrentHashMap<>();
    private static final Map<ObjectId, List<Model>> modelsByBrandCache = new ConcurrentHashMap<>();
    
    private static final Map<ObjectId, ModelSetup> modelSetupCache = new ConcurrentHashMap<>();
    private static final Map<String, ModelSetup> modelSetupByCodeCache = new ConcurrentHashMap<>();
    private static final Map<ObjectId, List<ModelSetup>> modelSetupsByBrandCache = new ConcurrentHashMap<>();
    private static final Map<ObjectId, List<ModelSetup>> modelSetupsByModelCache = new ConcurrentHashMap<>();

    // Cache status flags
    private static volatile boolean brandsLoaded = false;
    private static volatile boolean modelsLoaded = false;
    private static volatile boolean modelSetupsLoaded = false;

    /**
     * Initialize all caches on application startup
     */
    public static void initializeCache() {
        LOGGER.info("Initializing motorcycle data cache...");
        long startTime = System.currentTimeMillis();
        
        refreshBrandsCache();
        refreshModelsCache();
        refreshModelSetupsCache();
        
        long endTime = System.currentTimeMillis();
        LOGGER.info("motorcycle data cache initialized in {} ms. Brands: {}, Models: {}, ModelSetups: {}",
                   (endTime - startTime), brandCache.size(), modelCache.size(), modelSetupCache.size());
    }

    /**
     * Refresh brands cache from database
     */
    public static synchronized void refreshBrandsCache() {
        try {
            LOGGER.debug("Refreshing brands cache...");
            
            // Clear existing cache
            brandCache.clear();
            brandByCodeCache.clear();
            
            // Load all brands from database
            List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);
            
            for (Brand brand : brands) {
                if (brand.getId() != null) {
                    brandCache.put(brand.getId(), brand);
                }
                if (StringUtils.isNotBlank(brand.getCodice())) {
                    brandByCodeCache.put(brand.getCodice(), brand);
                }
            }
            
            brandsLoaded = true;
            LOGGER.debug("Brands cache refreshed. Loaded {} brands", brands.size());
            
        } catch (Exception ex) {
            LOGGER.error("Error refreshing brands cache", ex);
            brandsLoaded = false;
        }
    }

    /**
     * Refresh models cache from database
     */
    public static synchronized void refreshModelsCache() {
        try {
            LOGGER.debug("Refreshing models cache...");
            
            // Clear existing cache
            modelCache.clear();
            modelByCodeCache.clear();
            modelsByBrandCache.clear();
            
            // Load all models from database
            List<Model> models = BaseDao.getDocumentsByClass(Model.class);
            
            for (Model model : models) {
                if (model.getId() != null) {
                    modelCache.put(model.getId(), model);
                }
                if (StringUtils.isNotBlank(model.getCodice())) {
                    modelByCodeCache.put(model.getCodice(), model);
                }
                
                // Group by brand
                if (model.getBrandId() != null) {
                    modelsByBrandCache.computeIfAbsent(model.getBrandId(), k -> new ArrayList<>()).add(model);
                }
            }
            
            modelsLoaded = true;
            LOGGER.debug("Models cache refreshed. Loaded {} models", models.size());
            
        } catch (Exception ex) {
            LOGGER.error("Error refreshing models cache", ex);
            modelsLoaded = false;
        }
    }

    /**
     * Refresh model setups cache from database
     */
    public static synchronized void refreshModelSetupsCache() {
        try {
            LOGGER.debug("Refreshing model setups cache...");
            
            // Clear existing cache
            modelSetupCache.clear();
            modelSetupByCodeCache.clear();
            modelSetupsByBrandCache.clear();
            modelSetupsByModelCache.clear();
            
            // Load all model setups from database
            List<ModelSetup> modelSetups = BaseDao.getDocumentsByClass(ModelSetup.class);
            
            for (ModelSetup setup : modelSetups) {
                if (setup.getId() != null) {
                    modelSetupCache.put(setup.getId(), setup);
                }
                if (StringUtils.isNotBlank(setup.getCodice())) {
                    modelSetupByCodeCache.put(setup.getCodice(), setup);
                }
                
                // Group by brand
                if (setup.getBrandId() != null) {
                    modelSetupsByBrandCache.computeIfAbsent(setup.getBrandId(), k -> new ArrayList<>()).add(setup);
                }
                
                // Group by model
                if (setup.getModelId() != null) {
                    modelSetupsByModelCache.computeIfAbsent(setup.getModelId(), k -> new ArrayList<>()).add(setup);
                }
            }
            
            modelSetupsLoaded = true;
            LOGGER.debug("Model setups cache refreshed. Loaded {} model setups", modelSetups.size());
            
        } catch (Exception ex) {
            LOGGER.error("Error refreshing model setups cache", ex);
            modelSetupsLoaded = false;
        }
    }

    /**
     * Refresh all caches
     */
    public static void refreshAllCaches() {
        LOGGER.info("Refreshing all motorcycle data caches...");
        refreshBrandsCache();
        refreshModelsCache();
        refreshModelSetupsCache();
        LOGGER.info("All motorcycle data caches refreshed");
    }

    // ========== BRAND CACHE METHODS ==========

    /**
     * Get brand by ID from cache
     */
    public static Brand getBrandById(ObjectId brandId) {
        if (!brandsLoaded) {
            refreshBrandsCache();
        }
        return brandCache.get(brandId);
    }

    /**
     * Get brand by code from cache
     */
    public static Brand getBrandByCode(String code) {
        if (!brandsLoaded) {
            refreshBrandsCache();
        }
        return brandByCodeCache.get(code);
    }

    /**
     * Get all brands from cache
     */
    public static List<Brand> getAllBrands() {
        if (!brandsLoaded) {
            refreshBrandsCache();
        }
        return new ArrayList<>(brandCache.values());
    }

    // ========== MODEL CACHE METHODS ==========

    /**
     * Get model by ID from cache
     */
    public static Model getModelById(ObjectId modelId) {
        if (!modelsLoaded) {
            refreshModelsCache();
        }
        return modelCache.get(modelId);
    }

    /**
     * Get model by code from cache
     */
    public static Model getModelByCode(String code) {
        if (!modelsLoaded) {
            refreshModelsCache();
        }
        return modelByCodeCache.get(code);
    }

    /**
     * Get all models for a specific brand from cache
     */
    public static List<Model> getModelsByBrand(ObjectId brandId) {
        if (!modelsLoaded) {
            refreshModelsCache();
        }
        return modelsByBrandCache.getOrDefault(brandId, new ArrayList<>());
    }

    /**
     * Get all models from cache
     */
    public static List<Model> getAllModels() {
        if (!modelsLoaded) {
            refreshModelsCache();
        }
        return new ArrayList<>(modelCache.values());
    }

    // ========== MODEL SETUP CACHE METHODS ==========

    /**
     * Get model setup by ID from cache
     */
    public static ModelSetup getModelSetupById(ObjectId setupId) {
        if (!modelSetupsLoaded) {
            refreshModelSetupsCache();
        }
        return modelSetupCache.get(setupId);
    }

    /**
     * Get model setup by code from cache
     */
    public static ModelSetup getModelSetupByCode(String code) {
        if (!modelSetupsLoaded) {
            refreshModelSetupsCache();
        }
        return modelSetupByCodeCache.get(code);
    }

    /**
     * Get all model setups for a specific brand from cache
     */
    public static List<ModelSetup> getModelSetupsByBrand(ObjectId brandId) {
        if (!modelSetupsLoaded) {
            refreshModelSetupsCache();
        }
        return modelSetupsByBrandCache.getOrDefault(brandId, new ArrayList<>());
    }

    /**
     * Get all model setups for a specific model from cache
     */
    public static List<ModelSetup> getModelSetupsByModel(ObjectId modelId) {
        if (!modelSetupsLoaded) {
            refreshModelSetupsCache();
        }
        return modelSetupsByModelCache.getOrDefault(modelId, new ArrayList<>());
    }

    /**
     * Get all model setups from cache
     */
    public static List<ModelSetup> getAllModelSetups() {
        if (!modelSetupsLoaded) {
            refreshModelSetupsCache();
        }
        return new ArrayList<>(modelSetupCache.values());
    }

    // ========== UTILITY METHODS ==========

    /**
     * Check if caches are loaded
     */
    public static boolean isCacheLoaded() {
        return brandsLoaded && modelsLoaded && modelSetupsLoaded;
    }

    /**
     * Get cache statistics
     */
    public static Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("brands", brandCache.size());
        stats.put("models", modelCache.size());
        stats.put("modelSetups", modelSetupCache.size());
        return stats;
    }

    /**
     * Clear all caches
     */
    public static void clearAllCaches() {
        LOGGER.info("Clearing all motorcycle data caches...");
        
        brandCache.clear();
        brandByCodeCache.clear();
        
        modelCache.clear();
        modelByCodeCache.clear();
        modelsByBrandCache.clear();
        
        modelSetupCache.clear();
        modelSetupByCodeCache.clear();
        modelSetupsByBrandCache.clear();
        modelSetupsByModelCache.clear();
        
        brandsLoaded = false;
        modelsLoaded = false;
        modelSetupsLoaded = false;
        
        LOGGER.info("All motorcycle data caches cleared");
    }
}
