package pojo;

import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Country extends BasePojo {

    private String code;            //sigla_nazione
    private String codiceBelfiore;  //codice_belfiore
    private String description;     //denominazione_nazione

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCodiceBelfiore() {
        return codiceBelfiore;
    }

    public void setCodiceBelfiore(String codiceBelfiore) {
        this.codiceBelfiore = codiceBelfiore;
    }

    public String getDescription() {
        if (StringUtils.isBlank(description)) {
            return null;
        }
        return StringUtils.capitalize(description.toLowerCase());
    }

    public void setDescription(String description) {
        this.description = description;
    }
    
}
