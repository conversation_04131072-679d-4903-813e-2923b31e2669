package pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.Map;
import org.bson.BsonDocument;
import org.bson.types.ObjectId;

/**
 * POJO for storing backup documents from MongoDB Change Streams
 * 
 * <AUTHOR>
 */
public class BackupDocument {

    @JsonProperty("_id")
    private ObjectId id;
    
    private ObjectId realId;                    // ObjectId of the actual document in the main database
    private String resumeToken;                 // Resume token from change stream for ordering
    private String operationType;               // Type of operation (update, insert, delete, etc.)
    private Date creationDate;                  // When this backup was created
    private BsonDocument fullDocument;          // Full document after the change
    // Note: fullDocumentBeforeChange is not available in MongoDB Java Driver 3.12
    private BsonDocument updateDescription;     // Details of what changed (for updates)
    private Map<String, Object> metadata;       // Additional metadata

    public BackupDocument() {
        this.creationDate = new Date();
    }

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public ObjectId getRealId() {
        return realId;
    }

    public void setRealId(ObjectId realId) {
        this.realId = realId;
    }

    public String getResumeToken() {
        return resumeToken;
    }

    public void setResumeToken(String resumeToken) {
        this.resumeToken = resumeToken;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public BsonDocument getFullDocument() {
        return fullDocument;
    }

    public void setFullDocument(BsonDocument fullDocument) {
        this.fullDocument = fullDocument;
    }

    // Note: getFullDocumentBeforeChange() is not available in MongoDB Java Driver 3.12
    // These methods are commented out for compatibility

    public BsonDocument getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(BsonDocument updateDescription) {
        this.updateDescription = updateDescription;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
}
