package pojo;

/**
 * POJO representing a permission that can be assigned to users.
 * Permissions define what actions users can perform on specific resources.
 * 
 * <AUTHOR>
 */
public class Permission extends BasePojo {

    private String code;        // Unique identifier for the permission (e.g., "USER_MANAGEMENT")
    private String name;        // Human-readable name (e.g., "User Management")
    private String description; // Detailed description of what this permission allows

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
