package pojo;

/**
 * Composite POJO that combines Warranty with its related WarrantyType and InsuranceCompany
 * for complete warranty information display in search results and other contexts.
 * 
 * <AUTHOR>
 */
public class WarrantyEntry {
    
    private Warranty warranty;
    private WarrantyType warrantyType;
    private InsuranceCompany insuranceCompany;
    
    /**
     * Default constructor
     */
    public WarrantyEntry() {
    }
    
    /**
     * Constructor with all components
     * 
     * @param warranty The warranty object
     * @param warrantyType The warranty type object
     * @param insuranceCompany The insurance company object
     */
    public WarrantyEntry(Warranty warranty, WarrantyType warrantyType, InsuranceCompany insuranceCompany) {
        this.warranty = warranty;
        this.warrantyType = warrantyType;
        this.insuranceCompany = insuranceCompany;
    }
    
    /**
     * Get the warranty object
     * 
     * @return The warranty
     */
    public Warranty getWarranty() {
        return warranty;
    }
    
    /**
     * Set the warranty object
     * 
     * @param warranty The warranty to set
     */
    public void setWarranty(Warranty warranty) {
        this.warranty = warranty;
    }
    
    /**
     * Get the warranty type object
     * 
     * @return The warranty type
     */
    public WarrantyType getWarrantyType() {
        return warrantyType;
    }
    
    /**
     * Set the warranty type object
     * 
     * @param warrantyType The warranty type to set
     */
    public void setWarrantyType(WarrantyType warrantyType) {
        this.warrantyType = warrantyType;
    }
    
    /**
     * Get the insurance company object
     * 
     * @return The insurance company
     */
    public InsuranceCompany getInsuranceCompany() {
        return insuranceCompany;
    }
    
    /**
     * Set the insurance company object
     * 
     * @param insuranceCompany The insurance company to set
     */
    public void setInsuranceCompany(InsuranceCompany insuranceCompany) {
        this.insuranceCompany = insuranceCompany;
    }
    
    /**
     * Check if this entry has complete information
     * 
     * @return true if warranty, warrantyType, and insuranceCompany are all present
     */
    public boolean isComplete() {
        return warranty != null && warrantyType != null && insuranceCompany != null;
    }
    
    /**
     * Get a display string for this warranty entry
     * 
     * @return A formatted string representation
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("WarrantyEntry{");
        
        if (warranty != null) {
            sb.append("warranty=").append(warranty.getTitle()).append(" (").append(warranty.getCode()).append(")");
        } else {
            sb.append("warranty=null");
        }
        
        if (warrantyType != null) {
            sb.append(", warrantyType=").append(warrantyType.getName());
        } else {
            sb.append(", warrantyType=null");
        }
        
        if (insuranceCompany != null) {
            sb.append(", insuranceCompany=").append(insuranceCompany.getDescription());
        } else {
            sb.append(", insuranceCompany=null");
        }
        
        sb.append('}');
        return sb.toString();
    }
}
