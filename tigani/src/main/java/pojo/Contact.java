package pojo;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Contact extends BasePojo {

    private String firstName, lastName, fullName;       // fullName = firstName + " " + lastName, generated on save
    private String companyName, vatNumber;              // For companies
    private String sdi;                                 // SDI code for companies
    private String email, phoneNumber, note;
    private String contactType;
    private List<ObjectId> userIds;                     // List of user IDs associated with this contact

    // Person-specific fields
    private String birthCountryCode;                    // Country code from Country.java
    private String birthCity;                           // City name from City.java
    private String birthProvinceCode;                   // Province code from Province.java
    private Date birthDate;                             // Birth date
    private String gender;                              // Gender (MALE/FEMALE)
    private String tin;                                 // Tax identification number
    private String job;                                 // Job title (optional, select field)

    // Common address fields (for both PERSON and COMPANY)
    private String fullAddress;                         // Complete address text
    private String address;                             // Street address
    private String streetNumber;                        // Street number
    private String city;                                // City name (same as birthCity)
    private String postalCode;                          // Postal/ZIP code
    private String provinceCode;                        // Province code (same as birthProvinceCode)

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getSdi() {
        return sdi;
    }

    public void setSdi(String sdi) {
        this.sdi = sdi;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getContactType() {
        return contactType;
    }

    public void setContactType(String contactType) {
        this.contactType = contactType;
    }

    public List<ObjectId> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<ObjectId> userIds) {
        this.userIds = userIds;
    }

    public String getBirthCountryCode() {
        return birthCountryCode;
    }

    public void setBirthCountryCode(String birthCountryCode) {
        this.birthCountryCode = birthCountryCode;
    }

    public String getBirthCity() {
        return birthCity;
    }

    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    public String getBirthProvinceCode() {
        return birthProvinceCode;
    }

    public void setBirthProvinceCode(String birthProvinceCode) {
        this.birthProvinceCode = birthProvinceCode;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getFullAddress() {
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getStreetNumber() {
        return streetNumber;
    }

    public void setStreetNumber(String streetNumber) {
        this.streetNumber = streetNumber;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * Generates the fullName based on contact type.
     * For PERSON: firstName + " " + lastName
     * For COMPANY: companyName
     */
    public void generateFullName() {
        if (StringUtils.equalsIgnoreCase(this.contactType, "PERSON")) {
            if (StringUtils.isNotBlank(this.firstName) || StringUtils.isNotBlank(this.lastName)) {
                this.fullName = StringUtils.trim(
                    StringUtils.defaultString(this.firstName) + " " +
                    StringUtils.defaultString(this.lastName)
                );
            }
        } else if (StringUtils.equalsIgnoreCase(this.contactType, "COMPANY")) {
            this.fullName = this.companyName;
        }
    }
}
