package pojo;

import org.bson.types.ObjectId;
import java.util.List;
import java.util.Collections;

/**
 *
 * <AUTHOR>
 */
public class WarrantyDetails extends BasePojo {

    private ObjectId warrantyId;
    private List<String> provinceCode;
    private List<Integer> claimNumber;
    private ObjectId insuranceProvenanceTypeId;
    private List<Integer> universalClass;
    private Double premiumValue;

    public ObjectId getWarrantyId() {
        return warrantyId;
    }

    public void setWarrantyId(ObjectId warrantyId) {
        this.warrantyId = warrantyId;
    }

    public List<String> getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(List<String> provinceCode) {
        this.provinceCode = provinceCode;
    }

    public List<Integer> getClaimNumber() {
        return claimNumber;
    }

    public void setClaimNumber(List<Integer> claimNumber) {
        this.claimNumber = claimNumber;
    }

    public Integer getClaimNumberMin() {
        if (claimNumber == null || claimNumber.isEmpty()) {
            return null;
        }
        return Collections.min(claimNumber);
    }

    public Integer getClaimNumberMax() {
        if (claimNumber == null || claimNumber.isEmpty()) {
            return null;
        }
        return Collections.max(claimNumber);
    }

    public ObjectId getInsuranceProvenanceTypeId() {
        return insuranceProvenanceTypeId;
    }

    public void setInsuranceProvenanceTypeId(ObjectId insuranceProvenanceTypeId) {
        this.insuranceProvenanceTypeId = insuranceProvenanceTypeId;
    }

    public List<Integer> getUniversalClass() {
        return universalClass;
    }

    public void setUniversalClass(List<Integer> universalClass) {
        this.universalClass = universalClass;
    }

    public Integer getUniversalClassMin() {
        if (universalClass == null || universalClass.isEmpty()) {
            return null;
        }
        return Collections.min(universalClass);
    }

    public Integer getUniversalClassMax() {
        if (universalClass == null || universalClass.isEmpty()) {
            return null;
        }
        return Collections.max(universalClass);
    }

    public Double getPremiumValue() {
        return premiumValue;
    }

    public void setPremiumValue(Double premiumValue) {
        this.premiumValue = premiumValue;
    }
}
