package pojo;

import org.bson.types.ObjectId;

/**
 * POJO for tracking user access attempts to the website.
 * Records both successful and failed login attempts for security auditing.
 * 
 * <AUTHOR>
 */
public class AccessLog extends BasePojo {

    private ObjectId userId;        // User ID for successful logins, null for failed attempts with invalid username
    private String failureReason;   // Description of failure reason (e.g., "Invalid username", "Incorrect password")

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }
}
