package pojo;

/**
 * POJO to represent individual items in the Italiana API response payload
 * Contains codice and descrizione fields as returned by the API
 */
public class ItalianaApiItem {

    private String codice;
    private String descrizione;
    private Integer annoImmatricolazione, meseImmatricolazione;
    private Double valoreAssicurato;

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    public Integer getAnnoImmatricolazione() {
        return annoImmatricolazione;
    }

    public void setAnnoImmatricolazione(Integer annoImmatricolazione) {
        this.annoImmatricolazione = annoImmatricolazione;
    }

    public Integer getMeseImmatricolazione() {
        return meseImmatricolazione;
    }

    public void setMeseImmatricolazione(Integer meseImmatricolazione) {
        this.meseImmatricolazione = meseImmatricolazione;
    }

    public Double getValoreAssicurato() {
        return valoreAssicurato;
    }

    public void setValoreAssicurato(Double valoreAssicurato) {
        this.valoreAssicurato = valoreAssicurato;
    }

    @Override
    public String toString() {
        return "ItalianaApiItem{" +
                "codice='" + codice + '\'' +
                ", descrizione='" + descrizione + '\'' +
                '}';
    }
}
