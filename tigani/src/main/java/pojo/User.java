package pojo;

import enums.PermissionType;
import enums.ProfileType;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
public class User extends BasePojo {

    // basic info
    private String email;
    
    // profile
    private String name, lastname;
    private String phoneNumber;
    private List<String> languages;     // lingue parlate
    private Date birthDate;
    private String informations;

    // registration
    private Date registrationSendDate;
    private Date registrationDate;
    private String registrationToken;

    private String username;
    private String password;
    private String profileType;         // unconfirmed, customer, operator, admin, system
    private ObjectId imageId;

    // permissions
    private Map<String, List<String>> permissions; // Map of permission code to list of permission types

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public List<String> getLanguages() {
        return languages;
    }

    public void setLanguages(List<String> languages) {
        this.languages = languages;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getInformations() {
        return informations;
    }

    public void setInformations(String informations) {
        this.informations = informations;
    }

    public Date getRegistrationSendDate() {
        return registrationSendDate;
    }

    public void setRegistrationSendDate(Date registrationSendDate) {
        this.registrationSendDate = registrationSendDate;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public String getRegistrationToken() {
        return registrationToken;
    }

    public void setRegistrationToken(String registrationToken) {
        this.registrationToken = registrationToken;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public Map<String, List<String>> getPermissions() {
        return permissions;
    }

    public void setPermissions(Map<String, List<String>> permissions) {
        this.permissions = permissions;
    }

    /**
     * Check if the user has a specific permission type for a given permission code
     * @param permissionCode The permission code to check (e.g., "USER_MANAGEMENT")
     * @param permissionType The type of permission to check (VIEW, CREATE, EDIT, DELETE)
     * @return true if the user has the specified permission, false otherwise
     */
    public boolean hasPermission(String permissionCode, PermissionType permissionType) {
        if (permissions == null || permissionCode == null || permissionType == null) {
            return false;
        }
        if (StringUtils.equalsIgnoreCase(this.getProfileType(), ProfileType.SYSTEM.name())) {
            return true;
        }

        List<String> userPermissionTypes = permissions.get(permissionCode);
        if (userPermissionTypes == null) {
            return false;
        }

        return userPermissionTypes.contains(permissionType.getCode());
    }

    /**
     * Check if the user has a specific permission type for a given permission code
     * @param permissionCode The permission code to check (e.g., "USER_MANAGEMENT")
     * @param permissionType The type of permission to check as string (view, create, edit, delete)
     * @return true if the user has the specified permission, false otherwise
     */
    public boolean hasPermission(String permissionCode, String permissionType) {
        PermissionType type = PermissionType.getByCode(permissionType);
        return type != null && hasPermission(permissionCode, type);
    }

    /**
     * Initialize permissions map if null
     */
    public void initializePermissions() {
        if (permissions == null) {
            permissions = new HashMap<>();
        }
    }
}
