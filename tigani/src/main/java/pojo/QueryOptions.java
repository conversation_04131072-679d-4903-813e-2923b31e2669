package pojo;

import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class QueryOptions {
    private final Bson filter;
    private final Bson sort;
    private final int skip;
    private final int limit;

    public QueryOptions(Bson filter, Bson sort, int skip, int limit) {
        this.filter = filter;
        this.sort = sort;
        this.skip = skip;
        this.limit = limit;
    }

    public Bson getFilter() {
        return filter;
    }

    public Bson getSort() {
        return sort;
    }

    public int getSkip() {
        return skip;
    }

    public int getLimit() {
        return limit;
    }    
}
