package extensions;

import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.List;
import java.util.Map;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;
/**
 *
 * <AUTHOR>
 */
public class RemoveHtmlTagsFilter implements Filter {

    @Override
    public List<String> getArgumentNames() {
        return null;
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        if (input == null) {
            return null;
        }
        String html = (String) input;
        Document document = Jsoup.parse(html);
        String plainText = Jsoup.clean(document.body().html(), Whitelist.none());
        return plainText;
    }

}
