package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class LookupFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(LookupFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("language");
        names.add("checkPublished");
        names.add("publishedDate");
        names.add("skip");
        names.add("limit");
        names.add("distinct");
        names.add("orderBy"); //fieldname
        names.add("orderType"); //asc, desc, random
        return names;
    }

    @Override
    public List<Object> execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String table = (String) args.get("table");
        String language = (String) args.get("language");
        Boolean checkPublished = (Boolean) args.getOrDefault("checkPublished", true);
        int skip = ((Long) args.getOrDefault("skip", 0L)).intValue();
        int limit = ((Long) args.getOrDefault("limit", 0L)).intValue();
        String orderBy = (String) args.getOrDefault("orderBy", "creation");
        String orderType = (String) args.getOrDefault("orderType", "desc");
        String distinct = (String) args.get("distinct");
                
        Date publishedDate = null;
        if (args.get("publishedDate") != null) {
            String publishedDateString = (String) args.get("publishedDate");
            String format = "dd/MM/yyyy";
            try {
                publishedDate = DateUtils.parseDate(publishedDateString, format);
            } catch (ParseException ex) {
                LOGGER.error("Unparsable date " + publishedDateString);
            }
        }

        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }
        if (StringUtils.equalsIgnoreCase(language, "false")) {
            language = null;
        }

        return getElements(table, language, checkPublished, publishedDate, skip, limit, orderBy, orderType, distinct);
    }

    private List<Object> getElements(String table, String language, Boolean checkPublished, Date publishedDate, int skip, int limit, String orderBy, String orderType, String distinct) {
        List<Object> objs = null;

        try {
            Class<?> clazz = Class.forName("pojo." + table);
            List<Bson> filters = null;
            if (BooleanUtils.isTrue(checkPublished)) {
                filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, "published"));
                if (publishedDate != null) {
                    filters.add(DaoFilters.getFilter("publication", DaoFiltersOperation.GTE, publishedDate));
                }
            }
            
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);
            
            if (StringUtils.isNotBlank(distinct)) {
                // Esegui distinct
                if (StringUtils.isNotBlank(language)) {
                    objs = BaseDao.<Object>getDistinctDocumentsByField((Class<Object>) clazz, distinct, queryOptions, language);
                } else {
                    objs = BaseDao.<Object>getDistinctDocumentsByField((Class<Object>) clazz, distinct, queryOptions, null);
                }
            } else {
                if (StringUtils.isNotBlank(language)) {
                    if (queryOptions != null) {
                        objs = BaseDao.<Object>getDocumentsByFilters((Class<Object>) clazz, queryOptions, language);
                    } else {
                        objs = BaseDao.<Object>getDocuments((Class<Object>) clazz, language);
                    }
                } else {
                    if (queryOptions != null) {
                        objs = BaseDao.<Object>getDocumentsByFilters((Class<Object>) clazz, queryOptions);
                    } else {
                        objs = BaseDao.<Object>getDocuments((Class<Object>) clazz);
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Can't load items from collection " + table, ex);
        }

        return objs;
    }

}
