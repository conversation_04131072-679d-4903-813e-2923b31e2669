package utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class DateTimeUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DateTimeUtils.class.getName());
    
    public static Date stringToDate(String value, String pattern) {
        Date date = null;
        if (value != null) {
            try {
                date = DateUtils.parseDate(value, pattern);
            } catch (ParseException ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return date;
    }
    
    public static String dateToString(Date date, String pattern) {
        if (date != null) {
            DateFormat dateFormat = new SimpleDateFormat(pattern);
            return dateFormat.format(date);
        }
        return "";
    }
}
