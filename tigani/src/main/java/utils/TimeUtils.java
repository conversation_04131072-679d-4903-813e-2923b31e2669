package utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TimeUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(TimeUtils.class.getName());

    public static final Date BEGINNING_OF_TIMES = toDate("01/01/1900");
    public static final Date END_OF_TIMES = toDate("31/12/9999");

    public static Date toDate(String value) {
        return toDate(value, "dd/MM/yyyy");
    }

    public static Date toDate(String value, String pattern) {
        Date date = null;
        if (value != null) {
            try {
                date = DateUtils.parseDate(value, pattern);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return date;
    }

    public static Date toDate(String day, String month, String year) {
        Date date = null;
        if (StringUtils.isNotBlank(day) &&
                StringUtils.isNotBlank(month) &&
                StringUtils.isNotBlank(year)) {

            String value =
                    StringUtils.right("00" + day.trim(), 2) + "/" +
                            StringUtils.right("00" + month.trim(), 2) + "/" +
                            StringUtils.right("0000" + year.trim(), 4);

            try {
                date = DateUtils.parseDateStrictly(value, "dd/MM/yyyy");
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

        }
        return date;
    }

    public static Date toDate(String paramPrefix, Map<String, String> params) {
        if (StringUtils.isBlank(paramPrefix)) {
            return null;
        }
        if ((params == null) || params.isEmpty()) {
            return null;
        }
        if (!params.containsKey(paramPrefix + "Day") || !params.containsKey(paramPrefix + "Month") || !params.containsKey(paramPrefix + "Year")) {
            return null;
        }
        return TimeUtils.toDate(params.get(paramPrefix + "Day"), params.get(paramPrefix + "Month"), params.get(paramPrefix + "Year"));
    }

    public static String toString(Date value) {
        return toString(value, "dd/MM/yyyy");
    }

    public static String toString(Date value, String pattern) {
        String txt = null;
        if (value != null) {
            try {
                DateFormat dateFormat = new SimpleDateFormat(pattern, Locale.ITALIAN);
                txt = dateFormat.format(value);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return txt;
    }

    public static int year(Date date) {
        int year = 0;
        if (date != null) {
            Calendar cal = DateUtils.toCalendar(date);
            year = cal.get(Calendar.YEAR);
        }
        return year;
    }

    public static int month(Date date) {
        int month = 0;
        if (date != null) {
            Calendar cal = DateUtils.toCalendar(date);
            month = cal.get(Calendar.MONTH) + 1;
        }
        return month;
    }

    public static int day(Date date) {
        int year = 0;
        if (date != null) {
            Calendar cal = DateUtils.toCalendar(date);
            year = cal.get(Calendar.DAY_OF_MONTH);
        }
        return year;
    }

    public static int age(Date date) {
        int age = -1;
        if (date != null) {
            LocalDate today = LocalDate.now();
            LocalDate birthday = LocalDate.of(year(date), month(date), day(date));
            Period period = Period.between(birthday, today);
            age = period.getYears();
        }
        return age;
    }

    public static long diffInDays(Date d1, Date d2) {
        long diff = d2.getTime() - d1.getTime();
        long diffInSecond = TimeUnit.SECONDS.convert(diff, TimeUnit.MILLISECONDS);
        return (long) Math.ceil(diffInSecond / 86400D);
    }

    public static Date now() {
        return new Date();
    }

    public static Date today() {
        return DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    }

    public static Date yesterday() {
        return DateUtils.addDays(today(), -1);
    }

    public static Date tomorrow() {
        return DateUtils.addDays(today(), +1);
    }

    public static Date beginOfYear(Date date) {
        if (date != null) {
            date = DateUtils.setMonths(date, 0);
            date = DateUtils.setDays(date, 1);
        }
        return date;
    }

    public static Date endOfYear(Date date) {
        if (date != null) {
            date = DateUtils.setMonths(date, 11);
            date = DateUtils.setDays(date, 31);
        }
        return date;
    }

    public static Date beginOfMonth(Date date) {
        if (date != null) {
            date = DateUtils.setDays(date, 1);
        }
        return date;
    }

    public static Date endOfMonth(Date date) {
        if (date != null) {
            date = DateUtils.setDays(date, 1);
            date = DateUtils.addMonths(date, 1);
            date = DateUtils.addDays(date, -1);
        }
        return date;
    }

    public static Date beginOfDay(Date date) {
        if (date != null) {
            date = DateUtils.setHours(date, 0);
            date = DateUtils.setMinutes(date, 0);
            date = DateUtils.setSeconds(date, 0);
            date = DateUtils.setMilliseconds(date, 0);
        }
        return date;
    }

    public static Date endOfDay(Date date) {
        if (date != null) {
            date = DateUtils.setHours(date, 23);
            date = DateUtils.setMinutes(date, 59);
            date = DateUtils.setSeconds(date, 59);
            date = DateUtils.setMilliseconds(date, 999);
        }
        return date;
    }
}
