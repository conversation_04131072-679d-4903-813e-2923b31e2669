package utils;

import dao.BaseDaoBackup;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BackupDocument;
import service.ChangeStreamBackupService;

/**
 * Utility class for backup operations and convenience methods
 * 
 * <AUTHOR>
 */
public class BackupUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackupUtils.class.getName());

    /**
     * Get the complete change history for a document
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return List of backup documents showing the evolution of the document
     */
    public static List<BackupDocument> getDocumentHistory(ObjectId realId, String collectionName) {
        return BaseDaoBackup.getBackupsByRealId(realId, collectionName);
    }

    /**
     * Get the change history for a document within a specific time period
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @param fromDate Start date (inclusive)
     * @param toDate End date (inclusive)
     * @return List of backup documents within the date range
     */
    public static List<BackupDocument> getDocumentHistoryInDateRange(ObjectId realId, String collectionName, Date fromDate, Date toDate) {
        return BaseDaoBackup.getBackupsByRealIdAndDateRange(realId, collectionName, fromDate, toDate);
    }

    /**
     * Get the most recent backup for a document
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return The latest backup document or null if not found
     */
    public static BackupDocument getLatestDocumentBackup(ObjectId realId, String collectionName) {
        return BaseDaoBackup.getLatestBackupByRealId(realId, collectionName);
    }

    /**
     * Get recent changes for a collection
     * @param collectionName The name of the collection
     * @param limit Maximum number of results
     * @return List of recent backup documents
     */
    public static List<BackupDocument> getRecentChanges(String collectionName, int limit) {
        return BaseDaoBackup.getBackupsByCollection(collectionName, limit);
    }

    /**
     * Count how many backup versions exist for a document
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return Number of backup versions
     */
    public static long getDocumentBackupCount(ObjectId realId, String collectionName) {
        return BaseDaoBackup.countBackupsByRealId(realId, collectionName);
    }

    /**
     * Check if backup monitoring is enabled and running
     * @return true if backup monitoring is active
     */
    public static boolean isBackupMonitoringActive() {
        return Defaults.ENABLE_CHANGE_STREAMS_BACKUP && ChangeStreamBackupService.isRunning();
    }

    /**
     * Get the backup collection name
     * @return The name of the backup collection
     */
    public static String getBackupCollectionName() {
        return ChangeStreamBackupService.getBackupCollectionName();
    }

    /**
     * Check if a collection is being monitored for changes
     * @param collectionName The name of the collection to check
     * @return true if the collection is being monitored
     */
    public static boolean isCollectionMonitored(String collectionName) {
        return Defaults.CHANGE_STREAMS_COLLECTIONS != null &&
                Defaults.CHANGE_STREAMS_COLLECTIONS.stream().anyMatch(c -> c.equalsIgnoreCase(collectionName));
    }

    /**
     * Get list of monitored collections
     * @return List of collection names being monitored
     */
    public static List<String> getMonitoredCollections() {
        return Defaults.CHANGE_STREAMS_COLLECTIONS;
    }

    /**
     * Restart the backup monitoring service
     * This can be useful if configuration changes or if the service stops unexpectedly
     */
    public static void restartBackupMonitoring() {
        try {
            LOGGER.info("Restarting backup monitoring service...");
            ChangeStreamBackupService.stopChangeStreamMonitoring();
            
            // Wait a moment for cleanup
            Thread.sleep(1000);
            
            ChangeStreamBackupService.startChangeStreamMonitoring();
            LOGGER.info("Backup monitoring service restarted successfully");
        } catch (Exception ex) {
            LOGGER.error("Error restarting backup monitoring service", ex);
        }
    }

    /**
     * Get backup status information
     * @return String with current backup monitoring status
     */
    public static String getBackupStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Backup Monitoring Status:\n");
        status.append("- Enabled: ").append(Defaults.ENABLE_CHANGE_STREAMS_BACKUP).append("\n");
        status.append("- Running: ").append(ChangeStreamBackupService.isRunning()).append("\n");
        status.append("- Monitored Collections: ").append(Defaults.CHANGE_STREAMS_COLLECTIONS).append("\n");
        status.append("- Backup Collection: ").append(getBackupCollectionName()).append("\n");
        return status.toString();
    }

    /**
     * Extract field changes from update description
     * @param backup The backup document containing update description
     * @return String describing what fields were changed
     */
    public static String getChangesSummary(BackupDocument backup) {
        if (backup == null || backup.getUpdateDescription() == null) {
            return "No change details available";
        }

        StringBuilder summary = new StringBuilder();
        
        if (backup.getUpdateDescription().containsKey("updatedFields")) {
            summary.append("Updated fields: ");
            summary.append(backup.getUpdateDescription().get("updatedFields").toString());
        }
        
        if (backup.getUpdateDescription().containsKey("removedFields")) {
            if (summary.length() > 0) {
                summary.append("; ");
            }
            summary.append("Removed fields: ");
            summary.append(backup.getUpdateDescription().get("removedFields").toString());
        }
        
        // Note: truncatedArrays is not available in MongoDB Java Driver 3.12
        
        return summary.length() > 0 ? summary.toString() : "No specific changes recorded";
    }
}
