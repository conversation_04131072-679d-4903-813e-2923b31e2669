package utils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.security.InvalidParameterException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.commons.lang3.StringUtils;
import pojo.DocumentDescriptor;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;

/**
 *
 * <AUTHOR>
 */
public class FileUtils {
    
    public static final String ROOT = "/opt/" + Defaults.PROJECT_NAME + "/storage";
    
    public enum FileType {
        img (ROOT + "/img"),
        doc (ROOT + "/doc");
        
        private final String path;
        
        private FileType(String path) {
            this.path = path;
        }
        public String getPath() {
            return path;
        }
    }
    
    public static String composeFilename(FileType type, String filename, String extension) {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd-hhmmssSSS", Locale.ITALIAN);
        
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        return type + "-" + sanitizeFilename(filename) + "-" + dateFormat.format(now) + (StringUtils.isNotBlank(extension) ? extension : "");
    }
    
    public static String getPath(FileType type, Date date, String filename) {
        if (type == null) {
            return null;
        }
        if (date == null) {
            return null;
        }
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        
        return type.getPath() + "/" +
                StringUtils.right("0000" + calendar.get(Calendar.YEAR), 4) + "/" +
                StringUtils.right("00" + calendar.get(Calendar.MONTH), 2) + "/" +
                filename;
    }
    
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }

    public static void compressImage(DocumentDescriptor imageToCompress) {
        if (imageToCompress == null || imageToCompress.getContent() == null || imageToCompress.getContent().length == 0 ||
                StringUtils.isBlank(imageToCompress.getFilename()) || imageToCompress.getMetadata() == null) {
            return;
        }
        String contentType = imageToCompress.getMetadata().get("contentType");
        if (StringUtils.isBlank(contentType)) {
            return;
        }

        byte[] originalBytes = imageToCompress.getContent();
        String mime = contentType.toLowerCase();

        try {
            if (mime.contains("pdf")) {
                // Compressione PDF già esistente
                PdfReader reader = new PdfReader(originalBytes);
                ByteArrayOutputStream compressedPdf = new ByteArrayOutputStream();
                PdfStamper stamper = new PdfStamper(reader, compressedPdf, PdfWriter.VERSION_1_7);
                stamper.setFullCompression();
                stamper.close();
                reader.close();

                imageToCompress.getMetadata().put("contentType", "application/pdf");
                imageToCompress.setContent(compressedPdf.toByteArray());
                return;
            }

            if (!mime.contains("jpeg") && !mime.contains("jpg") && !mime.contains("png")) {
                return;
            }

            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(originalBytes));
            if (bufferedImage == null) {
                return;
            }

            // Calcolo compressione in base alla dimensione
            float quality = calculateCompressionQuality(originalBytes.length);

            ByteArrayOutputStream compressedImageBytes = new ByteArrayOutputStream();
            if (mime.contains("png")) {
                // Mantieni PNG con trasparenza, applica solo compressione se necessario
                // Verifica se l'immagine ha trasparenza
                boolean hasTransparency = bufferedImage.getColorModel().hasAlpha();

                if (hasTransparency) {
                    // Mantieni il formato PNG per preservare la trasparenza
                    ImageWriter pngWriter = ImageIO.getImageWritersByFormatName("png").next();
                    ImageWriteParam pngWriteParam = pngWriter.getDefaultWriteParam();

                    pngWriter.setOutput(ImageIO.createImageOutputStream(compressedImageBytes));
                    pngWriter.write(null, new IIOImage(bufferedImage, null, null), pngWriteParam);
                    pngWriter.dispose();

                    imageToCompress.setContent(compressedImageBytes.toByteArray());
                    // Mantieni il content type originale per PNG con trasparenza
                } else {
                    // PNG senza trasparenza, converti a JPEG per alleggerire
                    BufferedImage rgbImage = new BufferedImage(
                            bufferedImage.getWidth(),
                            bufferedImage.getHeight(),
                            BufferedImage.TYPE_INT_RGB
                    );
                    Graphics2D g = rgbImage.createGraphics();
                    g.setColor(Color.WHITE); // Sfondo bianco per PNG senza trasparenza
                    g.fillRect(0, 0, bufferedImage.getWidth(), bufferedImage.getHeight());
                    g.drawImage(bufferedImage, 0, 0, null);
                    g.dispose();

                    ImageWriter jpgWriter = ImageIO.getImageWritersByFormatName("jpg").next();
                    ImageWriteParam jpgWriteParam = jpgWriter.getDefaultWriteParam();
                    jpgWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                    jpgWriteParam.setCompressionQuality(quality);

                    jpgWriter.setOutput(ImageIO.createImageOutputStream(compressedImageBytes));
                    jpgWriter.write(null, new IIOImage(rgbImage, null, null), jpgWriteParam);
                    jpgWriter.dispose();

                    imageToCompress.setContent(compressedImageBytes.toByteArray());
                    // Aggiorna il content type a JPEG per PNG convertiti
                    imageToCompress.getMetadata().put("contentType", "image/jpeg");
                }
            } else {
                // Ricompressione JPEG
                ImageWriter jpgWriter = ImageIO.getImageWritersByFormatName("jpg").next();
                ImageWriteParam jpgWriteParam = jpgWriter.getDefaultWriteParam();
                jpgWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                jpgWriteParam.setCompressionQuality(quality);

                jpgWriter.setOutput(ImageIO.createImageOutputStream(compressedImageBytes));
                jpgWriter.write(null, new IIOImage(bufferedImage, null, null), jpgWriteParam);
                jpgWriter.dispose();

                imageToCompress.setContent(compressedImageBytes.toByteArray());
            }

//            Image image = Image.getInstance(compressedImageBytes.toByteArray());
//            image.setAlignment(Element.ALIGN_CENTER);
//
//            Document document = new Document();
//            ByteArrayOutputStream pdfOutputBytes = new ByteArrayOutputStream();
//            PdfWriter writer = PdfWriter.getInstance(document, pdfOutputBytes);
//            writer.setCompressionLevel(PdfStream.BEST_COMPRESSION);
//
//            document.open();
//
//            if (image.getWidth() > document.getPageSize().getWidth()) {
//                float scaler = ((document.getPageSize().getWidth() - document.leftMargin() - document.rightMargin()) / image.getWidth()) * 100;
//                image.scalePercent(scaler);
//            }
//
//            document.add(image);
//            document.close();
//
//            imagePdf.getMetadata().put("contentType", "application/pdf");
//            imagePdf.setContent(pdfOutputBytes.toByteArray());

        } catch (Exception ex) {
            // supressed
        }
    }

    private static float calculateCompressionQuality(long originalSize) {
        final long targetSize = 4L * 1024 * 1024; // 4 MB
        if (originalSize <= targetSize) return 0.9f;
        if (originalSize > 10L * 1024 * 1024) return 0.3f;
        if (originalSize > 8L * 1024 * 1024) return 0.4f;
        if (originalSize > 6L * 1024 * 1024) return 0.5f;
        if (originalSize > 5L * 1024 * 1024) return 0.6f;
        if (originalSize > 4L * 1024 * 1024) return 0.7f;
        return 0.8f;
    }
}
