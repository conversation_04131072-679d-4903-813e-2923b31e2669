package core;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.time.DateUtils;

/**
 *
 * <AUTHOR>
 */
public class DateJsonDeserializer extends JsonDeserializer<Date> {

    private static final long GMT_WORKAROUND = (1000 * 60 * 60);
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        ObjectCodec oc = p.getCodec();
        JsonNode node = oc.readTree(p);
        
        Date date = null;
        if (node.has("$date")) {
            if (node.get("$date").isLong()) {
                long millies = node.get("$date").asLong();
                if (millies != 0L) {
                    // dates can be a negative long value
                    date = new Date(millies - GMT_WORKAROUND);
                    if (TimeZone.getDefault().inDaylightTime(date)) {
                        date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                    }
                } else {
                    date = new Date(0L);
                }
            } else if (node.get("$date").isInt()) {
                // 01/01/1970 in "0", considered as int
                long millies = node.get("$date").asInt();
                if (millies != 0L) {
                    // dates can be a negative long value
                    date = new Date(millies - GMT_WORKAROUND);
                    if (TimeZone.getDefault().inDaylightTime(date)) {
                        date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                    }
                } else {
                    date = new Date(0L);
                }
            } else {
                try {
                    date = DateUtils.parseDate(node.get("$date").asText(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                    if (date != null) {
                        date = new Date(date.getTime() - GMT_WORKAROUND);
                        if (TimeZone.getDefault().inDaylightTime(date)) {
                            date = new Date(date.getTime() - GMT_WORKAROUND);
                        }
                    }
                } catch (ParseException ex) {
                    Logger.getLogger(DateJsonDeserializer.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } else {
            // ??????
        }

        return date;
    }
    
}
