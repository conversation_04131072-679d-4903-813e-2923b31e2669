package dao;

import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.bson.BsonDocument;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

import static com.mongodb.client.model.Filters.*;

/**
 *
 * <AUTHOR>
 */
public class DaoFilters {

    public static Bson getFilter(String fieldName, DaoFiltersOperation operation, Object value) {
        Bson filter = null;

        if (null != operation) switch (operation) {
            case EQ:
                filter = eq(fieldName, value);
                break;
            case NE:
                filter = ne(fieldName, value);
                break;
            case GTE:
                filter = gte(fieldName, value);
                break;
            case LTE:
                filter = lte(fieldName, value);
                break;
            case IN:
                if (value instanceof List) {
                    filter = in(fieldName, (List<?>) value);
                } else {
                    filter = in(fieldName, value);
                }
            default:
                break;
        }

        return filter;
    }
    
    public static QueryOptions createQueryWithOptions(List<Bson> filters, int skip, int limit, String orderBy, String orderType) {
        Bson combinedFilter = null;
        if (filters != null) {
            combinedFilter = filters.isEmpty() ? new BsonDocument() : Filters.and(filters);
        }
        Bson sort = null;

        if (orderBy != null && !orderBy.isEmpty()) {
            sort = StringUtils.equalsIgnoreCase(orderType, "desc") ? Sorts.descending(orderBy) : Sorts.ascending(orderBy);
        }

        return new QueryOptions(combinedFilter, sort, skip, limit);
    }    
}
