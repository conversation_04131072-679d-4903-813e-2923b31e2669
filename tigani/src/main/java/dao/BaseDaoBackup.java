package dao;

import static com.mongodb.MongoClient.getDefaultCodecRegistry;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import core.Core;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.bson.BsonDocument;
import org.bson.BsonValue;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BackupDocument;
import utils.Defaults;

/**
 * DAO class for querying backup documents created by MongoDB Change Streams
 * 
 * <AUTHOR>
 */
public class BaseDaoBackup {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseDaoBackup.class.getName());

    /**
     * Get all backup versions for a specific document by its real ID
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return List of backup documents ordered by creation date (oldest first)
     */
    public static List<BackupDocument> getBackupsByRealId(ObjectId realId, String collectionName) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName.toLowerCase());
            
            Bson filter = Filters.and(
                Filters.eq("realId", realId)
            );
            
            FindIterable<Document> documents = collection.find(filter)
                .sort(Sorts.ascending("creationDate"));
            
            List<BackupDocument> backups = new ArrayList<>();
            for (Document doc : documents) {
                BackupDocument backup = documentToBackupDocument(doc);
                if (backup != null) {
                    backups.add(backup);
                }
            }
            
            return backups;
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backups for realId: " + realId + ", collection: " + collectionName, ex);
            return new ArrayList<>();
        }
    }

    /**
     * Get backup versions for a specific document within a date range
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @param fromDate Start date (inclusive)
     * @param toDate End date (inclusive)
     * @return List of backup documents ordered by creation date
     */
    public static List<BackupDocument> getBackupsByRealIdAndDateRange(ObjectId realId, String collectionName, Date fromDate, Date toDate) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName.toLowerCase());
            
            List<Bson> filters = new ArrayList<>();
            filters.add(Filters.eq("realId", realId));
            
            if (fromDate != null) {
                filters.add(Filters.gte("creationDate", fromDate));
            }
            if (toDate != null) {
                filters.add(Filters.lte("creationDate", toDate));
            }
            
            Bson filter = Filters.and(filters);
            
            FindIterable<Document> documents = collection.find(filter)
                .sort(Sorts.ascending("creationDate"));
            
            List<BackupDocument> backups = new ArrayList<>();
            for (Document doc : documents) {
                BackupDocument backup = documentToBackupDocument(doc);
                if (backup != null) {
                    backups.add(backup);
                }
            }
            
            return backups;
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backups for realId: " + realId + ", collection: " + collectionName + " in date range", ex);
            return new ArrayList<>();
        }
    }

    /**
     * Get the latest backup for a specific document
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return The most recent backup document or null if not found
     */
    public static BackupDocument getLatestBackupByRealId(ObjectId realId, String collectionName) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName.toLowerCase());
            
            Bson filter = Filters.and(
                Filters.eq("realId", realId)
            );
            
            Document doc = collection.find(filter)
                .sort(Sorts.descending("creationDate"))
                .first();
            
            return documentToBackupDocument(doc);
        } catch (Exception ex) {
            LOGGER.error("Error retrieving latest backup for realId: " + realId + ", collection: " + collectionName, ex);
            return null;
        }
    }

    /**
     * Get backup by resume token
     * @param resumeToken The resume token from change stream
     * @return The backup document or null if not found
     */
    public static BackupDocument getBackupByResumeToken(String resumeToken) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            // Search across all monitored collections in the backup DB if needed
            for (String coll : Defaults.CHANGE_STREAMS_COLLECTIONS) {
                MongoCollection<Document> collection = backupDb.getCollection(coll.toLowerCase());
                Document doc = collection.find(Filters.eq("resumeToken", resumeToken)).first();
                if (doc != null) {
                    return documentToBackupDocument(doc);
                }
            }
            return null;
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backup by resume token: " + resumeToken, ex);
            return null;
        }
    }

    /**
     * Get all backups for a specific collection
     * @param collectionName The name of the original collection
     * @param limit Maximum number of results (0 for no limit)
     * @return List of backup documents ordered by creation date (newest first)
     */
    public static List<BackupDocument> getBackupsByCollection(String collectionName, int limit) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName.toLowerCase());
            
            FindIterable<Document> query = collection.find()
                .sort(Sorts.descending("creationDate"));
            
            if (limit > 0) {
                query = query.limit(limit);
            }
            
            List<BackupDocument> backups = new ArrayList<>();
            for (Document doc : query) {
                BackupDocument backup = documentToBackupDocument(doc);
                if (backup != null) {
                    backups.add(backup);
                }
            }
            
            return backups;
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backups for collection: " + collectionName, ex);
            return new ArrayList<>();
        }
    }

    /**
     * Convert MongoDB Document to BackupDocument POJO
     * @param doc MongoDB Document
     * @return BackupDocument or null if conversion fails
     */
    private static BackupDocument documentToBackupDocument(Document doc) {
        if (doc == null) {
            return null;
        }
        
        try {
            BackupDocument backup = new BackupDocument();
            backup.setId(doc.getObjectId("_id"));
            backup.setRealId(doc.getObjectId("realId"));
            backup.setResumeToken(doc.getString("resumeToken"));
            backup.setOperationType(doc.getString("operationType"));
            backup.setCreationDate(doc.getDate("creationDate"));
            
            // Handle BsonDocument fields with safe conversion
            Object fullDocVal = doc.get("fullDocument");
            if (fullDocVal != null) {
                if (fullDocVal instanceof BsonDocument) {
                    backup.setFullDocument((BsonDocument) fullDocVal);
                } else if (fullDocVal instanceof Document) {
                    backup.setFullDocument(((Document) fullDocVal).toBsonDocument(Document.class, getDefaultCodecRegistry()));
                } else if (fullDocVal instanceof String) {
                    try {
                        backup.setFullDocument(BsonDocument.parse((String) fullDocVal));
                    } catch (Exception ignore) {
                        // ignore parse errors
                    }
                }
            }
            // Note: fullDocumentBeforeChange is not available in MongoDB Java Driver 3.12
            Object updDescVal = doc.get("updateDescription");
            if (updDescVal != null) {
                if (updDescVal instanceof BsonDocument) {
                    backup.setUpdateDescription((BsonDocument) updDescVal);
                } else if (updDescVal instanceof Document) {
                    backup.setUpdateDescription(((Document) updDescVal).toBsonDocument(Document.class, getDefaultCodecRegistry()));
                } else if (updDescVal instanceof String) {
                    try {
                        backup.setUpdateDescription(BsonDocument.parse((String) updDescVal));
                    } catch (Exception ignore) {
                        // ignore parse errors
                    }
                }
            }
            
            // Handle metadata
            if (doc.get("metadata") != null) {
                backup.setMetadata(doc.get("metadata", Document.class));
            }
            
            return backup;
        } catch (Exception ex) {
            LOGGER.error("Error converting document to BackupDocument", ex);
            return null;
        }
    }

    /**
     * Count total backups for a specific document
     * @param realId The ObjectId of the document in the main collection
     * @param collectionName The name of the original collection
     * @return Number of backup versions
     */
    public static long countBackupsByRealId(ObjectId realId, String collectionName) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName.toLowerCase());
            
            Bson filter = Filters.and(
                Filters.eq("realId", realId)
            );
            
            return collection.countDocuments(filter);
        } catch (Exception ex) {
            LOGGER.error("Error counting backups for realId: " + realId + ", collection: " + collectionName, ex);
            return 0;
        }
    }
}
