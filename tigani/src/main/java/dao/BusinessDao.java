package dao;

import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import core.Core;
import enums.StatusType;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.*;

import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.*;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;

/**
 *
 * <AUTHOR>
 */
public class BusinessDao extends BaseDao {

    /**
     * Returns a list of distinct cities from Business entities.
     * Excludes cancelled and archived records.
     *
     * @return List of distinct city names
     * @throws Exception if an error occurs during database operation
     */
    public static List<String> getDistinctCities() throws Exception {
        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));
        filters.add(ne("city", null)); // Exclude null cities
        filters.add(ne("city", "")); // Exclude empty cities
        filters.add(eq("status", StatusType.CONFIRMED.name().toLowerCase()));

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("business");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        group("$city", sum("count", 1)),
                        project(fields(include("city"))),
                        sort(orderBy(ascending("city")))
                ));

        List<String> cities = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String city = document.getString("_id");
                if (StringUtils.isNotBlank(city)) {
                    cities.add(city);
                }
            }
        }

        Collections.sort(cities);

        return cities;
    }
}
