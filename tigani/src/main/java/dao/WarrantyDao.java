package dao;

import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.*;
import core.Core;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.WarrantyDetails;
import pojo.Warranty;
import pojo.InsuranceProvenanceType;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DAO class for Warranty-related custom queries
 * 
 * <AUTHOR>
 */
public class WarrantyDao extends BaseDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyDao.class.getName());

    /**
     * Checks if there are any existing WarrantyDetails that would conflict with the given warranty details.
     * A conflict occurs when there's an overlap in:
     * - Same warrantyId
     * - Same insuranceProvenanceTypeId  
     * - Overlapping provinceCode lists
     * - Overlapping claimNumber ranges
     * - Overlapping universalClass ranges
     * 
     * @param warrantyDetails The warranty details to validate
     * @return List of conflicting WarrantyDetails, empty if no conflicts
     * @throws Exception if validation fails
     */
    public static List<WarrantyDetails> findConflictingWarrantyDetails(WarrantyDetails warrantyDetails) throws Exception {
        if (warrantyDetails == null) {
            throw new InvalidParameterException("warrantyDetails cannot be null");
        }
        
        if (warrantyDetails.getWarrantyId() == null) {
            throw new InvalidParameterException("warrantyId cannot be null");
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("warrantydetails");
        
        List<Bson> filters = new ArrayList<>();
        
        // Must be same warranty
        filters.add(eq("warrantyId", warrantyDetails.getWarrantyId()));
        
        // Must be same insurance provenance type (if specified)
        if (warrantyDetails.getInsuranceProvenanceTypeId() != null) {
            filters.add(eq("insuranceProvenanceTypeId", warrantyDetails.getInsuranceProvenanceTypeId()));
        }
        
        // Exclude the current document if it's an update (has an ID)
        if (warrantyDetails.getId() != null) {
            filters.add(ne("_id", warrantyDetails.getId()));
        }
        
        // Standard filters
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

        // Find all potentially conflicting documents
        List<Document> docs = collection.find(and(filters)).into(new ArrayList<>());
        
        List<WarrantyDetails> conflictingDetails = new ArrayList<>();
        
        for (Document doc : docs) {
            WarrantyDetails existing = Core.fromDocument(doc, WarrantyDetails.class);
            
            if (hasOverlap(warrantyDetails, existing)) {
                conflictingDetails.add(existing);
            }
        }
        
        return conflictingDetails;
    }
    
    /**
     * Checks if two WarrantyDetails have overlapping criteria
     */
    private static boolean hasOverlap(WarrantyDetails newDetails, WarrantyDetails existingDetails) {
        // Check province code overlap
        if (hasListOverlap(newDetails.getProvinceCode(), existingDetails.getProvinceCode())) {
            // Check claim number overlap
            if (hasListOverlap(newDetails.getClaimNumber(), existingDetails.getClaimNumber())) {
                // Check universal class overlap
                if (hasListOverlap(newDetails.getUniversalClass(), existingDetails.getUniversalClass())) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Checks if two lists have any overlapping elements
     */
    private static <T> boolean hasListOverlap(List<T> list1, List<T> list2) {
        if (list1 == null || list1.isEmpty() || list2 == null || list2.isEmpty()) {
            return true;
        }
        
        for (T item1 : list1) {
            if (list2.contains(item1)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Finds all WarrantyDetails that match the given criteria values.
     * This method is used for testing warranty criteria to see which warranties
     * would be valid for specific input values.
     *
     * @param criteriaValues Map containing criteria field names and their values
     * @return List of matching WarrantyDetails with their associated Warranty information
     * @throws Exception if search fails
     */
    public static List<Map<String, Object>> findWarrantiesByCriteria(Map<String, Object> criteriaValues) throws Exception {
        if (criteriaValues == null || criteriaValues.isEmpty()) {
            throw new InvalidParameterException("criteriaValues cannot be null or empty");
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("warrantydetails");

        List<Bson> filters = new ArrayList<>();

        // Standard filters - only active warranty details
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

        // Build criteria filters
        for (Map.Entry<String, Object> entry : criteriaValues.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            switch (fieldName) {
                case "provinceCode":
                    if (value instanceof String && StringUtils.isNotBlank((String) value)) {
                        // Check if the province code is in the list
                        filters.add(in("provinceCode", (String) value));
                    }
                    break;

                case "claimNumber":
                    if (value instanceof Integer) {
                        // Check if the claim number is in the range (list contains the value)
                        filters.add(in("claimNumber", (Integer) value));
                    }
                    break;

                case "insuranceProvenanceTypeId":
                    if (value instanceof ObjectId) {
                        filters.add(eq("insuranceProvenanceTypeId", (ObjectId) value));
                    } else if (value instanceof String && StringUtils.isNotBlank((String) value)) {
                        try {
                            ObjectId oid = new ObjectId((String) value);
                            filters.add(eq("insuranceProvenanceTypeId", oid));
                        } catch (IllegalArgumentException e) {
                            LOGGER.warn("Invalid ObjectId format for insuranceProvenanceTypeId: " + value);
                        }
                    }
                    break;

                case "universalClass":
                    if (value instanceof Integer) {
                        // Check if the universal class is in the range (list contains the value)
                        filters.add(in("universalClass", (Integer) value));
                    }
                    break;
            }
        }

        // Find matching warranty details
        List<Document> docs = collection.find(and(filters)).into(new ArrayList<>());

        LOGGER.info("Found {} warranty details matching criteria: {}", docs.size(), criteriaValues);

        List<Map<String, Object>> results = new ArrayList<>();

        for (Document doc : docs) {
            try {
                WarrantyDetails warrantyDetails = Core.fromDocument(doc, WarrantyDetails.class);

                if (warrantyDetails == null || warrantyDetails.getWarrantyId() == null) {
                    continue;
                }

                // Get the associated warranty information
                Warranty warranty = BaseDao.getDocumentById(warrantyDetails.getWarrantyId(), Warranty.class);

                if (warranty != null) {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("warrantyDetails", warrantyDetails);
                    result.put("warranty", warranty);

                    // Add insurance provenance type information if available
                    if (warrantyDetails.getInsuranceProvenanceTypeId() != null) {
                        try {
                            InsuranceProvenanceType insuranceProvenanceType = BaseDao.getDocumentById(
                                warrantyDetails.getInsuranceProvenanceTypeId(), InsuranceProvenanceType.class);
                            if (insuranceProvenanceType != null) {
                                result.put("insuranceProvenanceType", insuranceProvenanceType);
                            }
                        } catch (Exception e) {
                            LOGGER.warn("Could not load insurance provenance type: " + warrantyDetails.getInsuranceProvenanceTypeId(), e);
                        }
                    }

                    results.add(result);
                }
            } catch (Exception e) {
                LOGGER.warn("Error processing warranty details document", e);
            }
        }

        return results;
    }
}
