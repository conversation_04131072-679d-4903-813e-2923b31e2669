{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_COMPANY' %}
{% set title = 'Impostazioni' %}

{% block extrahead %}
    <title>{{ title }}</title>

    <!-- Page script -->    
    {% include "be/include/snippets/plugins/filepond.html" %}
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}
    <script src="{{ contextPath }}/be/js/pages/settings-company.js?{{ buildNumber }}"></script>        
    <!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_SETTINGS_COMPANY', '{{ routes("BE_SETTINGS_COMPANY") }}');
    addVariables('imageId', '{{ company.logoImageId }}');
</script>

<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_SETTINGS_COMPANY_SAVE') %}
        {% if company.id is not empty %}
        {% set postUrl = routes('BE_SETTINGS_COMPANY_SAVE') + '?companyId=' + company.id %}
        {% endif %}
        <form id="company" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">Logo</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">                    

                    <div class="row mb-3">
                        <label class="col-lg-3 col-form-label">Logo:</label>
                        <div class="col-lg-9">      
                            <div class="row">
                                <div class="col-12 col-sm-8 col-md-6 col-lg-4">
                                <input id="logo" name="logo" type="file" class="filepond">
                            </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-text text-muted">Carica il tuo logo aziendale (250x100px).</div>                                
                                </div>
                            </div>
                        </div>
                        
                    </div>                    
                    <div class="row mb-3">
                        <label class="col-lg-3 col-form-label">Ragione sociale: <span class="text-danger">*</span></label>
                        <div class="col-lg-9">
                            <input name="fullname" type="text" class="form-control" placeholder="Ragione sociale" value="{{ company.fullname }}" required maxlength="150">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label class="col-lg-3 col-form-label">Telefono:</label>
                        <div class="col-lg-9">
                            <input name="phoneNumber" type="tel" class="form-control" placeholder="Telefono" value="{{ company.phoneNumber }}" maxlength="15">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label class="col-lg-3 col-form-label">Email: <span class="text-danger">*</span></label>
                        <div class="col-lg-9">
                            <input name="email" type="email" class="form-control" placeholder="Email" value="{{ company.email }}" required maxlength="150">
                        </div>
                    </div>                    

                    <div class="row mb-3">
                        <label class="col-lg-3 col-form-label">Partita IVA: <span class="text-danger">*</span></label>
                        <div class="col-lg-9">
                            <input name="vatNumber" type="text" class="form-control" placeholder="Partita IVA" value="{{ company.vatNumber }}" required maxlength="11">
                        </div>
                    </div>                     
                </div>
                <div class="card-footer d-flex justify-content-end align-items-center py-sm-2">                    
                    <div class="hstack gap-2 mt-0">                        
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>            

        </form>
    </div>
</div>


{% endblock %}
