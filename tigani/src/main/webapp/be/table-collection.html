{% extends "be/include/preline-base.html" %}

{% block extrahead %}
    
    {% set menu = 'MAINTENANCE' %}
    {% set submenu = 'TABLE_COLLECTION' %}

    <title>Manutenzione / Tabelle</title>
{% endblock %}

{% block content %}
<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <div class="max-w-6xl mx-auto">
        <div class="flex flex-col lg:flex-row bg-white rounded-xl border border-gray-200 shadow-xs dark:bg-neutral-800 dark:border-neutral-700">
            <div class="shrink-0">
                <div class="h-full lg:w-50 lg:border-e border-gray-200 dark:border-neutral-700">
                    
                    
                    <!-- Select Mobile Preline -->
                    <div class="p-4 lg:hidden">
                        <select id="category-select" class="hidden" data-hs-select='{
                                "placeholder": "Seleziona categoria...",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-2 pe-8 flex items-center text-nowrap w-full cursor-pointer bg-gray-100 text-gray-800 rounded-lg text-start text-sm dark:bg-neutral-800 dark:text-neutral-200",
                                "wrapperClasses": "lg:hidden",
                                "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-3\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-2 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }'>
                            <option value="all" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"14\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"9\" x=\"14\" y=\"12\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"3\" y=\"16\" rx=\"1\"/></svg>"
                                    }' selected>Tutte le Tabelle</option>

                            <option value="geography" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\"/><path d=\"M2 12h20\"/></svg>"
                                    }'>Dati Geografici</option>

                            <option value="vehicles" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H8.5a1 1 0 0 0-.8.4L5 11l-5.16 1.86a1 1 0 0 0-.84.99V16h3m0 0a2 2 0 1 0 4 0m10 0a2 2 0 1 0 4 0\"/></svg>"
                                    }'>Prodotti e Veicoli</option>

                            <option value="warranties" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/></svg>"
                                    }'>Garanzie</option>
                        </select>
                    </div>
                    
                    
                    <!-- Tab Navigation Desktop -->
                    <nav class="p-4 hidden lg:flex flex-col gap-1">
                        <button type="button" class="tab-btn w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg bg-gray-100 text-gray-800 focus:outline-none dark:bg-neutral-700 dark:text-neutral-200 transition-colors text-nowrap" data-category="all">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="7" height="9" x="3" y="3" rx="1"/>
                                <rect width="7" height="5" x="14" y="3" rx="1"/>
                                <rect width="7" height="9" x="14" y="12" rx="1"/>
                                <rect width="7" height="5" x="3" y="16" rx="1"/>
                            </svg>
                            Tutte le Tabelle
                        </button>

                        <button type="button" class="tab-btn w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 transition-colors text-nowrap" data-category="geography">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                                <path d="M2 12h20"/>
                            </svg>
                            Dati Geografici
                        </button>

                        <button type="button" class="tab-btn w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 transition-colors text-nowrap" data-category="vehicles">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H8.5a1 1 0 0 0-.8.4L5 11l-5.16 1.86a1 1 0 0 0-.84.99V16h3m0 0a2 2 0 1 0 4 0m10 0a2 2 0 1 0 4 0"/>
                            </svg>
                            Prodotti e Veicoli
                        </button>

                        <button type="button" class="tab-btn w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 transition-colors text-nowrap" data-category="warranties">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                            Garanzie
                        </button>
                    </nav>
                </div>
            </div>
            
            <div class="grow">
                <div class="p-5">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3" id="cards-grid">
                        <!-- Le tue card vanno qui -->
                        {% if user.hasPermission('COUNTRY_MANAGEMENT', 'view') %}
                            <!-- Card Paesi -->
                            <div data-category="all geography" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-blue-50 rounded-lg dark:bg-blue-900/20">
                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/><path d="M2 12h20"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Paesi
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Gestione anagrafica dei paesi e nazioni supportate dal sistema.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_COUNTRY_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('PROVINCE_MANAGEMENT', 'view') %}
                            <!-- Card Province -->
                            <div data-category="all geography" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-green-50 rounded-lg dark:bg-green-900/20">
                                        <svg class="shrink-0 size-4 text-green-600 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Province
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Gestione delle province e divisioni amministrative regionali.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_PROVINCE_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('RULEFIELD_MANAGEMENT', 'view') %}
                            <!-- Card Rule Field Descriptors -->
                            <div data-category="all system" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-blue-50 rounded-lg dark:bg-blue-900/20">
                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"/><path d="M9 22v-4h6v4"/><path d="M8 6h.01"/><path d="M16 6h.01"/><path d="M12 6h.01"/><path d="M12 10h.01"/><path d="M12 14h.01"/><path d="M16 10h.01"/><path d="M16 14h.01"/><path d="M8 10h.01"/><path d="M8 14h.01"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Rule Field Descriptors
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Gestione dei descrittori di campo per le regole di sistema.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_RULEFIELD_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('CITY_MANAGEMENT', 'view') %}
                            <!-- Card Città -->
                            <div data-category="all geography" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-purple-50 rounded-lg dark:bg-purple-900/20">
                                        <svg class="shrink-0 size-4 text-purple-600 dark:text-purple-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 21h18"/><path d="M5 21V7l8-4v18"/><path d="M19 21V11l-6-4"/><path d="M9 9v.01"/><path d="M9 12v.01"/><path d="M9 15v.01"/><path d="M9 18v.01"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Città
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Anagrafica completa delle città e comuni del territorio.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_CITY_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('BRAND_MANAGEMENT', 'view') %}
                            <!-- Card Marchi -->
                            <div data-category="all vehicles" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-orange-50 rounded-lg dark:bg-orange-900/20">
                                        <svg class="shrink-0 size-4 text-orange-600 dark:text-orange-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"/><path d="m9 12 2 2 4-4"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Marchi
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Gestione dei brand e marchi di veicoli supportati dal sistema.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_BRAND_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('MODEL_MANAGEMENT', 'view') %}
                            <!-- Card Modelli -->
                            <div data-category="all vehicles" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-red-50 rounded-lg dark:bg-red-900/20">
                                        <svg class="shrink-0 size-4 text-red-600 dark:text-red-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H8.5a1 1 0 0 0-.8.4L5 11l-5.16 1.86a1 1 0 0 0-.84.99V16h3m0 0a2 2 0 1 0 4 0m10 0a2 2 0 1 0 4 0"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Modelli
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Catalogo dei modelli di veicoli disponibili per ogni marchio.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_MODEL_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('MODELSETUP_MANAGEMENT', 'view') %}
                            <!-- Card Allestimenti Modelli -->
                            <div data-category="all vehicles" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-indigo-50 rounded-lg dark:bg-indigo-900/20">
                                        <svg class="shrink-0 size-4 text-indigo-600 dark:text-indigo-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Allestimenti Modelli
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Configurazioni e allestimenti specifici per ogni modello di veicolo.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_MODELSETUP_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'view') %}
                            <!-- Card Tipi di Garanzia -->
                            <div data-category="all warranties" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-teal-50 rounded-lg dark:bg-teal-900/20">
                                        <svg class="shrink-0 size-4 text-teal-600 dark:text-teal-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/><path d="m9 12 2 2 4-4"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Tipi di Garanzia
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Tipologie e categorie di garanzie disponibili nel sistema.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_WARRANTYTYPE_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                            {% if user.hasPermission('WARRANTY_MANAGEMENT', 'view') %}
                            <!-- Card Garanzie -->
                            <div data-category="all warranties" class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                <div class="h-full flex gap-x-5">
                                    <div class="shrink-0 size-8 flex items-center justify-center bg-emerald-50 rounded-lg dark:bg-emerald-900/20">
                                        <svg class="shrink-0 size-4 text-emerald-600 dark:text-emerald-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                                    </div>
                                    <div class="grow">
                                        <div class="h-full flex flex-col">
                                            <div>
                                                <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                    Garanzie
                                                </h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                    Gestione completa delle polizze e garanzie attive nel sistema.
                                                </p>
                                            </div>
                                            <div class="pt-1 mt-auto">
                                                <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                    Gestisci tabella
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <a class="after:absolute after:inset-0 after:z-10" href="{{ routes('BE_WARRANTY_COLLECTION') }}"></a>
                            </div>
                            {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block pagescript %}

<!-- Page Scripts -->
<script src="{{ contextPath }}/js/pages/table-collection.js?{{ buildNumber }}"></script>

{% endblock %}