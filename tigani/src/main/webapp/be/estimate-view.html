{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'ESTIMATE_COLLECTION' %}        
    
    <title>Trattativa P20250001021</title>

    <!-- Page Libs -->
    {% include "be/include/snippets/plugins/datatable.html" %}
    {% include "be/include/snippets/plugins/daterangepicker.html" %}
    {% include "be/include/snippets/plugins/filepond.html" %}    
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/clipboard.html" %}

{% endblock %}

{% block content %}
<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
<div class="max-w-6xl mx-auto">
      <!-- Breadcrumb -->
      <ol class="lg:hidden pt-3 md:pt-5 sm:pb-2 md:pb-0 px-2 sm:px-5 flex items-center whitespace-nowrap">
        <a href="{{ routes('BE_DEALER_COLLECTION') }}">
            <li class="flex items-center text-sm text-gray-600 dark:text-neutral-500">
                    Rivenditori
                    <svg class="shrink-0 overflow-visible size-4 ms-1.5 text-gray-400 dark:text-neutral-600" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                      <path d="M6 13L10 3" stroke="currentColor" stroke-linecap="round"></path>
                    </svg>
            </li>
        </a>          
        <li class="ps-1.5 flex items-center truncate font-semibold text-gray-800 dark:text-neutral-200 text-sm truncate">
          <span class="truncate">Trattativa P20250001021</span>
        </li>
      </ol>
      <!-- End Breadcrumb -->

      <div class="p-2 sm:p-5 sm:py-0 md:pt-5 space-y-5">
        <!-- User Profile Card -->
        <div class="p-3 md:p-5 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">         
          
              <div class="flex gap-3">
                  <!-- Icon -->
                  <span class="flex shrink-0 justify-center items-center size-16 bg-gray-100 font-medium text-xl text-gray-800 uppercase rounded-full dark:bg-neutral-800 dark:text-neutral-200">                      
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bike-icon lucide-bike"><circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/></svg>
                  </span>
                  <!-- End Icon -->

                  <div class="grow">
                      <div class="mt-1.5 flex flex-wrap items-end gap-y-3 gap-x-5">
                          <div class="grow">
                              <h1 class="font-medium text-xl text-gray-800 dark:text-neutral-200">
                                  Trattativa P20250001021
                              </h1>
                              
                                <div class="py-1 ps-1.5 pe-2.5 mt-2 inline-flex items-center gap-x-1.5 text-xs font-medium bg-sky-100 text-sky-800 rounded-full">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-icon lucide-loader"><path d="M12 2v4"/><path d="m16.2 7.8 2.9-2.9"/><path d="M18 12h4"/><path d="m16.2 16.2 2.9 2.9"/><path d="M12 18v4"/><path d="m4.9 19.1 2.9-2.9"/><path d="M2 12h4"/><path d="m4.9 4.9 2.9 2.9"/></svg>
                                    In corso
                                </div>                            
                          </div>

                          
                          <!-- Button Group -->
                          <div class="flex flex-wrap items-center gap-2">                              

                              <!-- Button -->
                              <div class="hs-tooltip inline-block">
                                  <button type="button" class="hs-tooltip-toggle size-8.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                                      <span class="sr-only">Email</span>
                                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>
                                  </button>
                                  <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip" data-placement="top" style="position: fixed; left: 903.929px; top: 31px;">
                                      Invia email
                                  </span>
                              </div>
                              <!-- End Button -->

                              <!-- Button -->
                              <div class="hs-tooltip inline-block">
                                  <button type="button" class="hs-tooltip-toggle size-8.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                                      <span class="sr-only">Chiama</span>
                                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                                  </button>
                                  <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip" data-placement="top" style="position: fixed; left: 972.609px; top: 31px;">
                                      Chiama
                                  </span>
                              </div>
                              <!-- End Button -->

                              <!-- Dropdown Link -->
                              <div class="hs-dropdown [--placement:bottom-right] relative inline-flex">
                                  <!-- Link -->
                                  <button id="hs-pro-pdmd" type="button" class="size-8.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                      <span class="sr-only">Customers</span>
                                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
                                  </button>
                                  <!-- End Link -->

                                  <!-- Dropdown Menu -->
                                  <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-44 z-10 transition-[opacity,margin] duration opacity-0 hidden bg-white rounded-xl shadow-lg dark:bg-neutral-950" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-pdmd" tabindex="-1">
                                      <div class="p-1 space-y-0.5">
                                          <a class="relative group py-2 px-3 flex items-center gap-x-2.5 text-[13px] text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#" target="_parent">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"></path></svg>
                                              Modifica
                                          </a>
                                          <a class="relative group py-2 px-3 flex items-center gap-x-2.5 text-[13px] text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="#" target="_parent">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"></rect><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"></path><path d="M10 12h4"></path></svg>
                                              Archivia
                                          </a>                                          
                                      </div>
                                  </div>
                                  <!-- End Dropdown Menu -->
                              </div>
                              <!-- End Dropdown Link -->
                          </div>
                          <!-- End Button Group -->
                      </div>
                  </div>
              </div>
              <!-- End Header -->          
          
          <!-- Header -->
          <div class="mt-4 md:mt-7 -mb-0.5 flex flex-col md:flex-row md:justify-between md:items-center gap-3">            

            <!-- Nav -->
            <div class="relative flex justify-center md:justify-start" data-hs-scroll-nav='{
                "autoCentering": true
              }'>
                <!-- Nav Tab -->
                <nav class="hs-scroll-nav-body flex flex-nowrap gap-x-1 overflow-x-auto [&::-webkit-scrollbar]:h-0 snap-x snap-mandatory pb-1.5" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                    <!-- Tab Overview -->
                    <button type="button" class="hs-tab-active:after:bg-gray-600 hs-tab-active:text-gray-800 hs-tab-active:font-medium hs-tab-active:dark:bg-neutral-800 hs-tab-active:dark:text-white hs-tab-active:dark:after:bg-neutral-200 snap-start relative inline-flex flex-nowrap items-center gap-x-2 px-2.5 py-1.5 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm whitespace-nowrap rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-0.5 after:inset-x-0 after:z-10 after:w-1/4 after:h-0.5 after:rounded-full after:mx-auto after:pointer-events-none dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 active" id="dealer-tab-overview" aria-selected="true" data-hs-tab="#dealer-view-overview" aria-controls="dealer-view-overview" role="tab">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"/>
                            <path d="M3 10a2 2 0 0 1 .709-1.528l7-6a2 2 0 0 1 2.582 0l7 6A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        </svg>
                        Panoramica
                    </button>                    

                    <!-- Tab Log -->
                    <button type="button" class="hs-tab-active:after:bg-gray-600 hs-tab-active:text-gray-800 hs-tab-active:font-medium hs-tab-active:dark:bg-neutral-800 hs-tab-active:dark:text-white hs-tab-active:dark:after:bg-neutral-200 snap-start relative inline-flex flex-nowrap items-center gap-x-2 px-2.5 py-1.5 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm whitespace-nowrap rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-0.5 after:inset-x-0 after:z-10 after:w-1/4 after:h-0.5 after:rounded-full after:mx-auto after:pointer-events-none dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="dealer-tab-log" aria-selected="false" data-hs-tab="#dealer-view-log" aria-controls="dealer-view-log" role="tab">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="5" width="6" height="6" rx="1"/>
                            <path d="m3 17 2 2 4-4"/>
                            <path d="M13 6h8"/>
                            <path d="M13 12h8"/>
                            <path d="M13 18h8"/>
                        </svg>
                        Log
                    </button>
                </nav>
                <!-- End Nav Tab -->
            </div>
            <!-- End Nav -->
          </div>
          <!-- End Header -->
        </div>
        <!-- End User Profile Card -->

        <!-- Sidebar Toggle -->
        <div class="xl:hidden flex justify-end">
          <button type="button" class="py-1.5 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-dupsd" aria-label="Sidebar Toggle" data-hs-overlay="#hs-pro-dupsd">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
              <line x1="15" x2="15" y1="3" y2="21" />
              <path d="m8 9 3 3-3 3" />
            </svg>
            Dettagli
          </button>
        </div>
        <!-- End Sidebar Toggle -->

        <!-- Tab Content -->
        <div id="dealer-view-overview" role="tabpanel" aria-labelledby="dealer-view-overview">
            {% include "be/include/snippets/pojo/estimate-view-overview.html" %}
        </div>
        <!-- End Tab Content -->               
        
        <!-- Tab Content -->
        <div id="dealer-view-log" class="hidden" role="tabpanel" aria-labelledby="dealer-view-log">
            {% include "be/include/snippets/pojo/estimate-view-log.html" %}
        </div>
        <!-- End Tab Content -->
        
      </div>
    </div>
  </div>
{% endblock %}
{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_DEALER_DATA', '{{ routes("BE_DEALER_DATA") }}');
        addRoute('BE_DEALER_OPERATE', '{{ routes("BE_DEALER_OPERATE") }}');
        addRoute('BE_DEALER_FORM', '{{ routes("BE_DEALER_FORM") }}');
        addRoute('BE_DEALER_SAVE', '{{ routes("BE_DEALER_SAVE") }}');
        addRoute('BE_DEALER_VIEW', '{{ routes("BE_DEALER_VIEW") }}');
        addRoute('BE_DEALER_COLLECTION', '{{ routes("BE_DEALER_COLLECTION") }}');
        addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    </script>

    <!-- Page Scripts -->    
    <script src="{{ contextPath }}/js/pages/dealer-form.js?{{ buildNumber }}"></script>
    
    <script>
        $(document).ready(function () {
            DealerForm.init()
        });
    </script>
       
{% endblock %}
