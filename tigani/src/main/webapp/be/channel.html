{% extends "be/include/base.html" %}

{% set page = 'CHANNEL' %}
{% set title = curChannel is empty ? 'Nuovo Canale' : 'Modifica Canale' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/channel.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_CHANNEL', '{{ routes("BE_CHANNEL") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curChannel is empty %}
            <h5 class="mb-0">Inserisci Canale</h5>
            {% else %}
            <h5 class="mb-0">Modifica Canale</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_CHANNEL_SAVE') %}
            {% if curChannel.id is not empty %}                
            {% set postUrl = routes('BE_CHANNEL_SAVE') + '?channelId=' + curChannel.id %}
            {% endif %}

            <form id="channel-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="code" type="text" class="form-control form-control-maxlength" placeholder="Codice identificativo" value="{{ curChannel.code }}" required maxlength="50">
                        <div class="form-text text-muted">Codice univoco per identificare il canale.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Azienda (agenzia/broker):</label>
                    <div class="col-lg-9">
                        <select name="companyId" class="form-control select" data-placeholder="Seleziona compagnia">
                            <option value=""></option>
                            {% set companies = lookup('Company', checkPublished=false, language='false') %}
                            {% if companies is not empty %}
                            {% for company in companies %}
                            <option value="{{ company.id }}" {{ curChannel.companyId equals company.id ? 'selected' : '' }}>{{ company.fullname }}</option>
                            {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona la compagnia di riferimento.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Limite Capacità:</label>
                    <div class="col-lg-9">
                        <input name="capacityLimit" type="number" class="form-control" placeholder="Limite di capacità" value="{{ curChannel.capacityLimit }}" min="0">
                        <div class="form-text text-muted">Limite massimo di capacità del canale.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Brand Models:</label>
                    <div class="col-lg-9">
                        <select name="brandModelIds" class="form-control select" data-placeholder="Seleziona brand models" multiple>
                            {% set brandModels = lookup('BrandModel', checkPublished=false, language='false') %}
                            {% if brandModels is not empty %}
                                {% for brandModel in brandModels %}
                                    <option value="{{ brandModel.id }}" {% if curChannel.brandModelIds is not empty %}{% for selectedId in curChannel.brandModelIds %}{{ selectedId equals brandModel.id ? 'selected' : '' }}{% endfor %}{% endif %}>{{ brandModel.name }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona i brand models associati al canale.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Garanzie:</label>
                    <div class="col-lg-9">
                        <select name="warrantyIds" class="form-control select" data-placeholder="Seleziona garanzie" multiple>
                            {% set warranties = lookup('Warranty', checkPublished=false, language='false') %}
                            {% if warranties is not empty %}
                                {% for warranty in warranties %}
                                    <option value="{{ warranty.id }}" {% if curChannel.warrantyIds is not empty %}{% for selectedId in curChannel.warrantyIds %}{{ selectedId equals warranty.id ? 'selected' : '' }}{% endfor %}{% endif %}>{{ warranty.title }} ({{ warranty.code }})</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona le garanzie associate al canale.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipi Provenienza Assicurativa:</label>
                    <div class="col-lg-9">
                        <select name="insuranceProvenanceTypeIds" class="form-control select" data-placeholder="Seleziona tipi provenienza" multiple>
                            {% set insuranceProvenanceTypes = lookup('InsuranceProvenanceType', checkPublished=false, language='false') %}
                            {% if insuranceProvenanceTypes is not empty %}
                                {% for insuranceProvenanceType in insuranceProvenanceTypes %}
                                    <option value="{{ insuranceProvenanceType.id }}" {% if curChannel.insuranceProvenanceTypeIds is not empty %}{% for selectedId in curChannel.insuranceProvenanceTypeIds %}{{ selectedId equals insuranceProvenanceType.id ? 'selected' : '' }}{% endfor %}{% endif %}>{{ insuranceProvenanceType.title }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona i tipi di provenienza assicurativa.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_CHANNEL_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curChannel is empty %}
                        Crea Canale
                        {% else %}
                        Aggiorna Canale
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
