{% extends "be/include/base.html" %}

{% set page = 'WARRANTYTYPE' %}
{% set title = curWarrantyType is empty ? 'Nuovo Tipo di Garanzia' : 'Modifica Tipo di Garanzia' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/warrantytype.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_WARRANTYTYPE', '{{ routes("BE_WARRANTYTYPE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curWarrantyType is empty %}
            <h5 class="mb-0">Inserisci Tipo di Garanzia</h5>
            {% else %}
            <h5 class="mb-0">Modifica Tipo di Garanzia</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_WARRANTYTYPE_SAVE') %}
            {% if curWarrantyType.id is not empty %}                
            {% set postUrl = routes('BE_WARRANTYTYPE_SAVE') + '?warrantyTypeId=' + curWarrantyType.id %}
            {% endif %}

            <form id="warrantytype-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="code" type="text" class="form-control form-control-maxlength" placeholder="Codice identificativo" value="{{ curWarrantyType.code }}" required maxlength="50">
                        <div class="form-text text-muted">Codice univoco per identificare il tipo di garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Nome: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="name" type="text" class="form-control form-control-maxlength" placeholder="Nome del tipo di garanzia" value="{{ curWarrantyType.name }}" required maxlength="100">
                        <div class="form-text text-muted">Nome descrittivo del tipo di garanzia.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Icona:</label>
                    <div class="col-lg-9">
                        <input name="icon" type="text" class="form-control form-control-maxlength" placeholder="ph-shield, ph-check, etc." value="{{ curWarrantyType.icon }}" maxlength="50">
                        <div class="form-text text-muted">Classe CSS dell'icona (es. ph-shield, ph-check, ph-star).</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_WARRANTYTYPE_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curWarrantyType is empty %}
                        Crea Tipo Garanzia
                        {% else %}
                        Aggiorna Tipo Garanzia
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
