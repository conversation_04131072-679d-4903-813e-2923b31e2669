/**
 * Users DataTable Initialization
 * Manually initializes the Preline UI DataTable component
 */ import {HSDataTable} from "preline";

(function() {
    'use strict';

    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeDataTable();
        setupEventHandlers();
    });

    /**
     * Initialize the DataTable
     */
    function initializeDataTable() {
        try {
            // Check if HSDataTable is available
            if (typeof HSDataTable === 'undefined') {
                console.error('HSDataTable is not available. Make sure Preline JS is loaded.');
                return;
            }

            // Find the datatable element
            const datatableElement = document.querySelector('[data-hs-datatable]');
            if (!datatableElement) {
                console.error('DataTable element not found');
                return;
            }

            // Configuration for the datatable
            const config = {
                pageLength: 10,
                searching: true,
                ordering: true,
                paging: true,
                info: true,
                select: true,
                pagingOptions: {
                    pageBtnClasses: "min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700"
                },
                selecting: true,
                rowSelectingOptions: {
                    selectAllSelector: "#hs-table-search-checkbox-all"
                },
                language: {
                    zeroRecords: '<div class="py-10 px-5 flex flex-col justify-center items-center text-center"><svg class="shrink-0 size-6 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg><div class="max-w-sm mx-auto"><p class="mt-2 text-sm text-gray-600 dark:text-neutral-400">No search results</p></div></div>',
                    search: "",
                    searchPlaceholder: "Search for users...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    lengthMenu: "Show _MENU_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            };

            // Initialize the datatable
            const hsDataTable = new HSDataTable(datatableElement, config);

            // Store reference for later use
            window.usersDataTable = hsDataTable;

            console.log('DataTable initialized successfully');

            // Setup search functionality
            setupSearchFunctionality(hsDataTable);

        } catch (error) {
            console.error('Error initializing DataTable:', error);

            // Fallback: Try auto-initialization
            if (typeof HSDataTable.autoInit === 'function') {
                console.log('Attempting auto-initialization as fallback...');
                HSDataTable.autoInit();
            }
        }
    }

    /**
     * Setup search functionality
     */
    function setupSearchFunctionality(hsDataTable) {
        const searchInput = document.getElementById('hs-table-input-search');
        if (searchInput && hsDataTable.dataTable) {
            // Clear any existing event listeners
            searchInput.removeEventListener('input', handleSearch);

            // Add new event listener
            searchInput.addEventListener('input', handleSearch);

            function handleSearch(e) {
                const searchTerm = e.target.value;
                hsDataTable.dataTable.search(searchTerm).draw();
            }
        }
    }

    /**
     * Setup additional event handlers
     */
    function setupEventHandlers() {
        // Handle delete buttons
        document.addEventListener('click', function(e) {
            if (e.target.textContent === 'Delete') {
                handleDeleteUser(e);
            }
        });

        // Handle select all checkbox
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function(e) {
                handleSelectAll(e.target.checked);
            });
        }

        // Handle individual row checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.hasAttribute('data-hs-datatable-row-selecting-individual')) {
                updateSelectAllState();
            }
        });
    }

    /**
     * Handle delete user action
     */
    function handleDeleteUser(event) {
        event.preventDefault();

        const row = event.target.closest('tr');
        const userName = row.querySelector('td:nth-child(2)').textContent.trim();

        if (confirm(`Are you sure you want to delete ${userName}?`)) {
            // Remove the row from the table
            if (window.usersDataTable && window.usersDataTable.dataTable) {
                window.usersDataTable.dataTable.row(row).remove().draw();
            } else {
                row.remove();
            }

            console.log(`User ${userName} deleted`);
        }
    }

    /**
     * Handle select all functionality
     */
    function handleSelectAll(isChecked) {
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        individualCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
    }

    /**
     * Update select all checkbox state based on individual selections
     */
    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === individualCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    }

    /**
     * Utility function to refresh the datatable
     */
    function refreshDataTable() {
        if (window.usersDataTable && window.usersDataTable.dataTable) {
            window.usersDataTable.dataTable.draw();
        }
    }

    // Expose utility functions globally if needed
    window.usersDataTableUtils = {
        refresh: refreshDataTable,
        getSelectedRows: function() {
            const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
            return Array.from(checkedBoxes).map(checkbox => checkbox.closest('tr'));
        },
        clearSelection: function() {
            const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual], #hs-table-search-checkbox-all');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            });
        }
    };

})();
