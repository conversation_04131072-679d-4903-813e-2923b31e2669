{% extends "be/include/preline-base.html" %}

{% block content %}
    {% set area = 'MAINTENANCE' %}
    {% set page = 'MODELSETUP' %}
    {% set menu = 'MAINTENANCE' %}
    {% set submenu = 'MODELSETUP' %}

    <title>Manutenzione / Allestimento Modello</title>

    <!-- Page Header -->
    <div class="sticky top-0 inset-x-0 z-20 bg-white border-y px-4 sm:px-6 lg:px-8 lg:hidden dark:bg-neutral-800 dark:border-neutral-700">
        <div class="flex items-center py-2">
            <!-- Navigation Toggle -->
            <button type="button" class="size-8 flex justify-center items-center gap-x-2 border border-gray-200 text-gray-800 hover:text-gray-500 rounded-lg focus:outline-none focus:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:border-neutral-700 dark:text-neutral-200 dark:hover:text-neutral-500 dark:focus:text-neutral-500" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-application-sidebar" aria-label="Toggle navigation" data-hs-overlay="#hs-application-sidebar">
                <span class="sr-only">Toggle Navigation</span>
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M9 3v18"/></svg>
            </button>
            <!-- End Navigation Toggle -->

            <!-- Breadcrumb -->
            <ol class="ms-3 flex items-center whitespace-nowrap">
                <li class="flex items-center text-sm text-gray-800 dark:text-neutral-400">
                    Manutenzione
                    <svg class="shrink-0 mx-3 overflow-visible size-2.5 text-gray-400 dark:text-neutral-500" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 1L10.6869 7.16086C10.8637 7.35239 10.8637 7.64761 10.6869 7.83914L5 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </li>
                <li class="text-sm font-semibold text-gray-800 truncate dark:text-neutral-400" aria-current="page">
                    Allestimento Modello
                </li>
            </ol>
            <!-- End Breadcrumb -->
        </div>
    </div>
    <!-- End Page Header -->

    <!-- Content -->
    <div class="w-full lg:ps-64">
        <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">
            <!-- Page Title -->
            <div class="max-w-4xl">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    Allestimento Modello
                </h2>
            </div>

            <!-- Form Content -->
            <div class="max-w-4xl">
                {% if curModelSetup is not empty %}
                    {% include "be/include/snippets/pojo/modelsetup-form.html" %}
                {% else %}
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-neutral-400">Allestimento non trovato</p>
                        <a href="{{ routes('BE_MODELSETUP_COLLECTION') }}" class="mt-4 inline-flex items-center gap-x-2 text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-500 dark:hover:text-blue-400">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                            Torna alla lista
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <!-- End Content -->

{% endblock %}

{% block scripts %}
    <script class="reload-script-on-load">
        addRoute('BE_MODELSETUP', '{{ routes("BE_MODELSETUP") }}');
        addRoute('BE_MODELSETUP_SAVE', '{{ routes("BE_MODELSETUP_SAVE") }}');
        addRoute('BE_MODELSETUP_OPERATE', '{{ routes("BE_MODELSETUP_OPERATE") }}');
    </script>

    <!-- Page Scripts -->
    <script src="{{ contextPath }}/js/pages/modelsetup-form.js?{{ buildNumber }}"></script>
{% endblock %}
