<button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#table-filters">
    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"/></svg>
    Filtra
    <span id="filter-counter" class="ps-2 text-xs font-semibold text-blue-600 border-s border-gray-200 dark:border-neutral-700 dark:text-blue-500" style="display: none;">
        0
    </span>
</button>

<script>
// Dynamic Filter Counter
(function() {
    'use strict';

    function updateFilterCounter() {
        const filterOffcanvas = document.getElementById('table-filters');
        if (!filterOffcanvas) return;

        // Count all checked checkboxes and selected options in the filter offcanvas
        const checkedInputs = filterOffcanvas.querySelectorAll('input[type="checkbox"]:checked, input[type="radio"]:checked');
        const selectedSelects = filterOffcanvas.querySelectorAll('select option:checked:not([value=""])');
        const filledTextInputs = filterOffcanvas.querySelectorAll('input[type="text"], input[type="search"]');

        let activeFilters = checkedInputs.length + selectedSelects.length;

        // Count non-empty text inputs
        filledTextInputs.forEach(input => {
            if (input.value && input.value.trim() !== '') {
                activeFilters++;
            }
        });

        const counter = document.getElementById('filter-counter');
        if (counter) {
            counter.textContent = activeFilters;

            // Show/hide counter based on active filters
            if (activeFilters > 0) {
                counter.style.display = 'inline';
            } else {
                counter.style.display = 'none';
            }
        }
    }

    // Initialize counter on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Initial count
        setTimeout(updateFilterCounter, 100);

        // Listen for changes in the filter offcanvas
        const filterOffcanvas = document.getElementById('table-filters');
        if (filterOffcanvas) {
            // Use event delegation to catch all input changes
            filterOffcanvas.addEventListener('change', updateFilterCounter);
            filterOffcanvas.addEventListener('input', updateFilterCounter);
            filterOffcanvas.addEventListener('keyup', updateFilterCounter);
        }

        // Also listen for changes in the main document (in case filters are outside offcanvas)
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[type="checkbox"], input[type="radio"], select')) {
                updateFilterCounter();
            }
        });

        document.addEventListener('input', function(e) {
            if (e.target.matches('input[type="text"], input[type="search"]')) {
                updateFilterCounter();
            }
        });
    });

    // Make updateFilterCounter globally accessible for manual updates
    window.updateFilterCounter = updateFilterCounter;
})();
</script>