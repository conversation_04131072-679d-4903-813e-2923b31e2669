<!-- JS -->
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-resize.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-crop.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-transform.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-preview.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-edit.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-filter.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-exif-orientation.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-validate-size.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-file-validate-size.min.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-file-validate-type.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond-plugin-file-encode.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/filepond.min.js"></script>
<script src="{{ contextPath }}/js/libs/filepond/locale/it-it.js"></script>
<script src="{{ contextPath }}/js/libs/doka/doka.min.js"></script>
<!-- /JS -->
<!-- CSS -->
<link href="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-preview.css" rel="stylesheet">
<link href="{{ contextPath }}/js/libs/filepond/filepond-plugin-image-edit.css" rel="stylesheet">
<link href="{{ contextPath }}/js/libs/filepond/filepond.min.css" rel="stylesheet">
<link href="{{ contextPath }}/js/libs/doka/doka.min.css" rel="stylesheet">
<!-- /CSS -->