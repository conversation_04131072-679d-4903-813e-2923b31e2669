<!-- Overview -->
<div class="xl:p-5 flex flex-col xl:bg-white xl:border xl:border-gray-200 xl:shadow-2xs xl:rounded-xl dark:xl:bg-neutral-800 dark:xl:border-neutral-700">
    <!-- Grid -->
    <div class="xl:flex">
        <!-- Activity Offcanvas -->
        <div id="hs-pro-dupsd" class="hs-overlay [--auto-close:xl]
             hs-overlay-open:translate-x-0
             -translate-x-full transition-all duration-300 transform
             w-80
             hidden
             fixed inset-y-0 start-0 z-60
             bg-white border-e border-gray-200
             xl:relative xl:z-0 xl:block xl:translate-x-0 xl:end-auto xl:bottom-0
             overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500
             dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1" aria-labelledby="hs-pro-dupsd-label">
            <div class="p-5 xl:p-0">
                <div class="xl:hidden">
                    <!-- Close Button -->
                    <div class="absolute top-3 end-3 z-10">
                        <button type="button" class="size-8 shrink-0 flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#hs-pro-dupsd">
                            <span class="sr-only">Close</span>
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18" />
                                <path d="m6 6 12 12" />
                            </svg>
                        </button>
                    </div>
                    <!-- End Close Button -->
                </div>

                <!-- Body -->
                <div class="xl:pe-4 mt-3 divide-y divide-gray-200 dark:divide-neutral-700">
                    <div class="py-4 first:pt-0 last:pb-0">
                        <h2 id="hs-pro-dupsd-label" class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                            Informazioni
                        </h2>

                        <!-- List -->
                        <ul class="mt-3 space-y-2">
                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z" />
                                            <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2" />
                                            <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2" />
                                            <path d="M10 6h4" />
                                            <path d="M10 10h4" />
                                            <path d="M10 14h4" />
                                            <path d="M10 18h4" />
                                        </svg>
                                        <span id="company-name">Twin S.r.l.</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-name" data-clipboard-action="copy" data-clipboard-success-text="Ragione sociale copiata">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                                            <circle cx="12" cy="10" r="3" />
                                        </svg>
                                        <span id="company-address">Via Pezze 13, 31033, Castelfranco Veneto (TV)</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-address" data-clipboard-action="copy" data-clipboard-success-text="Indirizzo copiato">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo Telefono -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                        </svg>
                                        <span id="company-phone">+39 ************</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-phone" data-clipboard-action="copy" data-clipboard-success-text="Telefono copiato">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia telefono</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo Email -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="20" height="16" x="2" y="4" rx="2" />
                                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                                        </svg>
                                        <span id="company-email"><EMAIL></span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-email" data-clipboard-action="copy" data-clipboard-success-text="Email copiata">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo Website (con link) -->
                                    <a class="inline-flex items-center gap-x-3 text-sm text-gray-800 hover:text-blue-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-neutral-200 dark:hover:text-blue-500" href="https://hdbergamo.it" target="_blank">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                                        </svg>
                                        <span id="company-website">https://hdbergamo.it</span>
                                    </a>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-website" data-clipboard-action="copy" data-clipboard-success-text="Website copiato">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia</span>
                                        </span>
                                    </button>
                                </div>
                            </li>                         

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo P.IVA -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
                                            <rect width="20" height="14" x="2" y="6" rx="2"/>
                                        </svg>
                                        <span id="company-vat">125065321564</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-vat" data-clipboard-action="copy" data-clipboard-success-text="P.IVA copiata">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo Codice Fiscale -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                            <line x1="9" y1="13" x2="15" y2="13"/>
                                            <line x1="9" y1="17" x2="15" y2="17"/>
                                        </svg>
                                        <span id="company-cf">****************</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-cf" data-clipboard-action="copy" data-clipboard-success-text="Codice Fiscale copiato">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia Codice Fiscale</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo SDI -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                                        </svg>
                                        <span id="company-sdi">M5UXCR1</span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-sdi" data-clipboard-action="copy" data-clipboard-success-text="SDI copiato">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-md shadow-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia SDI</span>
                                        </span>
                                    </button>
                                </div>
                            </li>

                            <li>
                                <div class="flex items-center justify-between gap-x-3">
                                    <!-- Icona + Testo PEC -->
                                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                                        <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="20" height="16" x="2" y="4" rx="2" />
                                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                                        </svg>
                                        <span id="company-pec"><EMAIL></span>
                                    </div>

                                    <!-- Bottone Copy -->
                                    <button type="button" class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip size-6 shrink-0 inline-flex justify-center items-center gap-x-2 rounded-md text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden sm:focus:bg-gray-100 dark:text-neutral-400 sm:dark:hover:bg-neutral-700" data-clipboard-target="#company-pec" data-clipboard-action="copy" data-clipboard-success-text="PEC copiata">
                                        <svg class="js-clipboard-default shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                        </svg>
                                        <svg class="js-clipboard-success hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity hidden invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded-2xs dark:bg-neutral-700" role="tooltip">
                                            <span class="js-clipboard-success-text">Copia PEC</span>
                                        </span>
                                    </button>
                                </div>
                            </li>


                        </ul>
                        <!-- End List -->
                    </div>

                    <div class="py-4 first:pt-0 last:pb-0">
                        <h2 class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                            Brand gestiti
                        </h2>

                        <!-- Button Group -->
                        <div class="mt-2 space-y-1">
                            <!-- Dropdown -->
                            <div class="hs-dropdown [--trigger:hover] [--placement:top] relative inline-block">
                                <div id="hs-pro-dupodcd1" class="hs-dropdown-toggle py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown" role="button">
                                    <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">                          
                                        Harley Davidson
                                </div>

                                <div class="hs-dropdown-menu transition-opacity duration hs-dropdown-open:opacity-100 opacity-0 hidden z-10 w-auto bg-white rounded-xl shadow-xl dark:bg-neutral-900 dark:text-neutral-400 before:absolute before:bottom-full before:left-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dupodcd1">
                                    <!-- Header -->
                                    <div class="p-4 flex items-center gap-x-3 border-b border-gray-200 dark:border-neutral-700">
                                        <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-11" width="32" height="32">                          
                                            <div class="grow">
                                                <h4 class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                                    Harley Davison
                                                </h4>
                                                <p class="text-xs text-gray-500 dark:text-neutral-500">
                                                    3 persone abilitate
                                                </p>
                                            </div>                            
                                    </div>
                                    <!-- End Header -->

                                    <!-- Body -->
                                    <div class="p-4 flex flex-1 items-center gap-x-5 text-gray-600 dark:text-neutral-400 w-full">
                                        <div class="flex flex-col flex-1 w-full">
                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1708443683276-8a3eb30faef2?q=80&amp;w=160&amp;h=160&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Andrea
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1601935111741-ae98b2b230b0?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=3&amp;w=160&amp;h=160&amp;q=80" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Leonardo Furlan
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <span class="flex shrink-0 justify-center items-center size-6 bg-gray-200 text-gray-500 text-[11px] font-semibold rounded-full dark:bg-neutral-600 dark:border-neutral-700 dark:text-neutral-400">L</span>

                                                <div class="grow">
                                                    <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                        Marco Zen
                                                    </span>
                                                    <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                        <EMAIL>
                                                    </small>
                                                </div>


                                            </div>
                                            <!-- End Item -->
                                        </div>
                                    </div>
                                    <!-- End Body -->
                                </div>
                            </div>
                            <!-- End Dropdown -->

                            <!-- Dropdown -->
                            <div class="hs-dropdown [--trigger:hover] [--placement:top] relative inline-block">
                                <div id="hs-pro-dupodcd2" class="hs-dropdown-toggle py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown" role="button">
                                    <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-4" width="32" height="32">                          
                                        Ducati
                                </div>

                                <div class="hs-dropdown-menu transition-opacity duration hs-dropdown-open:opacity-100 opacity-0 hidden z-10 w-auto bg-white rounded-xl shadow-xl dark:bg-neutral-900 dark:text-neutral-400 before:absolute before:bottom-full before:left-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dupodcd2">
                                    <!-- Header -->
                                    <div class="p-4 flex items-center gap-x-3 border-b border-gray-200 dark:border-neutral-700">
                                        <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-11" width="32" height="32">                          
                                            <div class="grow">
                                                <h4 class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                                    Ducati
                                                </h4>
                                                <p class="text-xs text-gray-500 dark:text-neutral-500">
                                                    3 persone abilitate
                                                </p>
                                            </div>

                                    </div>
                                    <!-- End Header -->

                                    <!-- Body -->
                                    <div class="p-4 flex flex-1 items-center gap-x-5 text-gray-600 dark:text-neutral-400 w-full">
                                        <div class="flex flex-col flex-1 w-full">
                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1708443683276-8a3eb30faef2?q=80&amp;w=160&amp;h=160&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Andrea
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1601935111741-ae98b2b230b0?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=3&amp;w=160&amp;h=160&amp;q=80" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Leonardo Furlan
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <span class="flex shrink-0 justify-center items-center size-6 bg-gray-200 text-gray-500 text-[11px] font-semibold rounded-full dark:bg-neutral-600 dark:border-neutral-700 dark:text-neutral-400">L</span>

                                                <div class="grow">
                                                    <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                        Marco Zen
                                                    </span>
                                                    <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                        <EMAIL>
                                                    </small>
                                                </div>


                                            </div>
                                            <!-- End Item -->
                                        </div>
                                    </div>
                                    <!-- End Body -->
                                </div>
                            </div>
                            <!-- End Dropdown -->

                            <!-- Dropdown -->
                            <div class="hs-dropdown [--trigger:hover] [--placement:top] relative inline-block">
                                <div id="hs-pro-dupodcd3" class="hs-dropdown-toggle py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown" role="button">
                                    <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-4" width="32" height="32">                          
                                        Triumph
                                </div>

                                <div class="hs-dropdown-menu transition-opacity duration hs-dropdown-open:opacity-100 opacity-0 hidden z-10 w-auto bg-white rounded-xl shadow-xl dark:bg-neutral-900 dark:text-neutral-400 before:absolute before:bottom-full before:left-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dupodcd3">
                                    <!-- Header -->
                                    <div class="p-4 flex items-center gap-x-3 border-b border-gray-200 dark:border-neutral-700">
                                        <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-11" width="32" height="32">                          
                                            <div class="grow">
                                                <h4 class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                                    Triumph
                                                </h4>
                                                <p class="text-xs text-gray-500 dark:text-neutral-500">
                                                    3 persone abilitate
                                                </p>
                                            </div>

                                    </div>
                                    <!-- End Header -->

                                    <!-- Body -->
                                    <div class="p-4 flex flex-1 items-center gap-x-5 text-gray-600 dark:text-neutral-400 w-full">
                                        <div class="flex flex-col flex-1 w-full">
                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1708443683276-8a3eb30faef2?q=80&amp;w=160&amp;h=160&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Andrea
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <img class="shrink-0 mx-auto size-6 rounded-full" src="https://images.unsplash.com/photo-1601935111741-ae98b2b230b0?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=3&amp;w=160&amp;h=160&amp;q=80" alt="Avatar">

                                                    <div class="grow">
                                                        <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                            Leonardo Furlan
                                                        </span>
                                                        <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                            <EMAIL>
                                                        </small>
                                                    </div>


                                            </div>
                                            <!-- End Item -->

                                            <!-- Item -->
                                            <div class="flex justify-between items-center gap-3 py-2.5 first:pt-0 last:pb-0 first:border-t-0 border-t border-dashed border-gray-200 dark:border-neutral-700">
                                                <span class="flex shrink-0 justify-center items-center size-6 bg-gray-200 text-gray-500 text-[11px] font-semibold rounded-full dark:bg-neutral-600 dark:border-neutral-700 dark:text-neutral-400">L</span>

                                                <div class="grow">
                                                    <span class="block text-sm text-gray-800 dark:text-neutral-200">
                                                        Marco Zen
                                                    </span>
                                                    <small class="block text-xs text-gray-500 dark:text-neutral-400">
                                                        <EMAIL>
                                                    </small>
                                                </div>


                                            </div>
                                            <!-- End Item -->
                                        </div>
                                    </div>
                                    <!-- End Body -->
                                </div>
                            </div>
                            <!-- End Dropdown -->
                        </div>
                        <!-- Button Group -->
                    </div>

                    <div class="py-4 first:pt-0 last:pb-0">
                        <h2 class="mb-3 text-sm font-semibold text-gray-800 dark:text-neutral-200">
                            Team
                        </h2>

                        <!-- List Group -->
                        <ul class="space-y-3">
                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <span class="flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 text-gray-700 text-xs font-medium uppercase rounded-full dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                        LF
                                    </span>
                                    <div class="grow">
                                        <a class="block text-sm font-medium text-gray-800 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                            Leonardo Furlan
                                        </a>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">
                                            <EMAIL>
                                        </p>
                                    </div>                            
                                </div>
                            </li>
                            <!-- End List Item -->

                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-1670272505340-d906d8d77d03?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2.5&w=320&h=320&q=80" alt="Avatar">
                                        <div class="grow">
                                            <a class="block text-sm font-medium text-gray-800 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                                Andrea Ballan
                                            </a>
                                            <p class="text-xs text-gray-500 dark:text-neutral-500">
                                                <EMAIL>
                                            </p>
                                        </div>                          
                                </div>
                            </li>
                            <!-- End List Item -->

                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <span class="flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 text-gray-700 text-xs font-medium uppercase rounded-full dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                        MZ
                                    </span>
                                    <div class="grow">
                                        <a class="block text-sm font-medium text-gray-800 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                            Marco Zen
                                        </a>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">
                                            <EMAIL>
                                        </p>
                                    </div>

                                </div>
                            </li>
                            <!-- End List Item -->


                        </ul>
                        <!-- End List Group -->
                    </div>

                </div>
                <!-- End Body -->
            </div>
        </div>
        <!-- End Activity Offcanvas -->

        <!-- Content -->
        <div class="xl:ps-5 grow space-y-5">
            <!-- Sales Card -->
            <div class="p-5 space-y-3 flex flex-col bg-white border border-gray-200 rounded-xl shadow-2xs xl:shadow-none dark:bg-neutral-800 dark:border-neutral-700">
                <!-- Header -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                    <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                        Vendite
                    </h2>

                    <div id="reportrange" class="py-1.5 px-3 whitespace-nowrap inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 cursor-pointer disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 ">
                        <!-- Calendar Icon -->
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                            <line x1="16" x2="16" y1="2" y2="6"/>
                            <line x1="8" x2="8" y1="2" y2="6"/>
                            <line x1="3" x2="21" y1="10" y2="10"/>
                        </svg>

                        <!-- Date Range Text -->
                        <span class="capitalize">01/01/2024 - 31/01/2024</span>

                        <!-- Caret Down Icon -->
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m6 9 6 6 6-6"/>
                        </svg>
                    </div>
                </div>
                <!-- End Header -->

                <!-- Stats Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 md:gap-4">
                    <!-- Card -->
                    <div class="p-4 flex flex-col border border-gray-200 rounded-xl dark:border-neutral-700">
                        <h2 class="text-sm text-gray-500 dark:text-neutral-500">
                            Polizze emesse
                        </h2>

                        <div class="flex items-center gap-x-1.5">
                            <p class="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                                68
                            </p>
                            <span class="inline-flex items-center gap-x-1 text-sm text-red-600 rounded-full dark:text-red-500">
                                4.9%
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="22 17 13.5 8.5 8.5 13.5 2 7" />
                                    <polyline points="16 17 22 17 22 11" />
                                </svg>
                            </span>
                        </div>
                    </div>
                    <!-- End Card -->

                    <!-- Card -->
                    <div class="p-4 flex flex-col border border-gray-200 rounded-xl dark:border-neutral-700">
                        <h2 class="text-sm text-gray-500 dark:text-neutral-500">
                            Fatturato totale
                        </h2>

                        <div class="flex items-center gap-x-1.5">
                            <p class="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                                € 5.990,18
                            </p>
                            <span class="inline-flex items-center gap-x-1 text-sm text-teal-600 rounded-full dark:text-teal-500">
                                25.8%
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
                                    <polyline points="16 7 22 7 22 13" />
                                </svg>
                            </span>
                        </div>
                    </div>
                    <!-- End Card -->

                    <!-- Card -->
                    <div class="p-4 flex flex-col border border-gray-200 rounded-xl dark:border-neutral-700">
                        <h2 class="text-sm text-gray-500 dark:text-neutral-500">
                            Commissioni
                        </h2>

                        <div class="flex items-center gap-x-1.5">
                            <p class="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                                € 2.543
                            </p>
                            <span class="inline-flex items-center gap-x-1 text-sm text-teal-600 rounded-full dark:text-teal-500">
                                90.3%
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
                                    <polyline points="16 7 22 7 22 13" />
                                </svg>
                            </span>
                        </div>
                    </div>
                    <!-- End Card -->
                </div>
                <!-- End Stats Grid -->

                <!-- Progress -->
                <div class="my-4">

                    <!-- Legend Indicator -->
                    <div class="mb-1 flex justify-between items-center">
                        <div class="inline-flex items-center w-1/4">
                            <span class="hidden sm:inline-block shrink-0 size-2.5 bg-red-500 rounded-sm me-1.5"></span>
                            <span class="text-sm text-gray-800 dark:text-neutral-200">
                                Scarse
                            </span>
                        </div>
                        <div class="inline-flex items-center w-1/4">
                            <span class="hidden sm:inline-block shrink-0 size-2.5 bg-orange-500 rounded-sm me-1.5"></span>
                            <span class="text-sm text-gray-800 dark:text-neutral-200">
                                Moderate
                            </span>
                        </div>
                        <div class="inline-flex items-center w-1/4">
                            <span class="hidden sm:inline-block shrink-0 size-2.5 bg-yellow-200 rounded-sm me-1.5"></span>
                            <span class="text-sm text-gray-800 dark:text-neutral-200">
                                Buone
                            </span>
                        </div>
                        <div class="inline-flex items-center w-1/4">
                            <span class="hidden sm:inline-block shrink-0 size-2.5 bg-teal-400 rounded-sm me-1.5"></span>
                            <span class="text-sm text-gray-800 dark:text-neutral-200">
                                Eccellenti
                            </span>
                        </div>
                    </div>
                    <!-- End Legend Indicator -->

                    <!-- Progress -->
                    <div class="relative">
                        <div class="flex w-full h-2.5 bg-gray-200 rounded-full overflow-hidden dark:bg-neutral-700">
                            <div class="flex flex-col justify-center overflow-hidden bg-linear-to-r from-red-500 via-yellow-400 to-teal-400 text-xs text-white text-center whitespace-nowrap w-full" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="absolute top-1/2 start-[38%] w-2 h-5 bg-orange-500 border-2 border-white rounded-full transform -translate-y-1/2 dark:border-neutral-800"></div>
                    </div>
                    <!-- End Progress -->
                </div>
                <!-- End Progress -->
            </div>
            <!-- End Sales Card -->

            <!-- Products Card -->
            <div class="flex flex-col bg-white border border-gray-200 rounded-xl shadow-2xs xl:shadow-none dark:bg-neutral-800 dark:border-neutral-700">
                <!-- Header -->
                <div class="p-5 pb-2 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                        Attività recente
                    </h2>                  
                    <!-- Form Group -->
                    <div class="flex sm:justify-end items-center gap-x-2">
                        <!-- Button -->
                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"></rect><path d="m3 17 2 2 4-4"></path><path d="M13 6h8"></path><path d="M13 12h8"></path><path d="M13 18h8"></path></svg>
                            Vedi tutto
                        </button>   
                        <!-- End Button -->
                    </div>
                    <!-- End Form Group -->
                </div>
                <!-- End Header -->

                <!-- Timeline -->
                <div class="px-5 max-h-100 overflow-y-auto
                     [&::-webkit-scrollbar]:w-2
                     [&::-webkit-scrollbar-track]:rounded-full
                     [&::-webkit-scrollbar-track]:bg-gray-100
                     [&::-webkit-scrollbar-thumb]:rounded-full
                     [&::-webkit-scrollbar-thumb]:bg-gray-300
                     dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                     dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                    <!-- Vertical Steps -->
                    <div class="pb-5">
                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        27 Settembre 2025 ore 10.43
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"/><path d="m3 17 2 2 4-4"/><path d="M13 6h8"/><path d="M13 12h8"/><path d="M13 18h8"/></svg>
                                </span>

                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Andrea Ballan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">iniziato</span>
                                            <span class="text-gray-500 dark:text-neutral-500">la</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">quotazione</span>
                                            <span class="text-gray-500 dark:text-neutral-500">con protocollo</span>
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <line x1="10" y1="9" x2="8" y2="9"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    P2025010025
                                                </span>
                                            </a>
                                        </span>

                                        <!-- List Group Polizza -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Prodotto -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
                                                            <rect width="20" height="14" x="2" y="6" rx="2"/>
                                                        </svg>
                                                    </span>
                                                    Prodotto
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Moto Protezione Premium
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Premio -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="12" cy="12" r="10"/>
                                                            <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"/>
                                                            <path d="M12 18V6"/>
                                                        </svg>
                                                    </span>
                                                    Premio
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    € 485,00
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Veicolo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                                        </svg>                                                        
                                                    </span>
                                                    Veicolo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Yamaha MT-09
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Scadenza -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8 2v4"/>
                                                            <path d="M16 2v4"/>
                                                            <rect width="18" height="18" x="3" y="4" rx="2"/>
                                                            <path d="M3 10h18"/>
                                                        </svg>
                                                    </span>
                                                    Scadenza
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    15/10/2025
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Cliente (Link) -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Cliente
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <a href="#" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline">
                                                    Mario Rossi
                                                </a>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Stato -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                                            <path d="m9 11 3 3L22 4"/>
                                                        </svg>
                                                    </span>
                                                    Stato
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="inline-flex items-center gap-x-1.5 py-1 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">
                                                    <span class="size-1.5 inline-block rounded-full bg-green-800 dark:bg-green-500"></span>
                                                    Polizza emessa
                                                </span>
                                            </div>
                                            <!-- End Item -->
                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        26 Settembre 2025 ore 11.02
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"/><path d="m3 17 2 2 4-4"/><path d="M13 6h8"/><path d="M13 12h8"/><path d="M13 18h8"/></svg>
                                </span>

                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Andrea Ballan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">iniziato</span>
                                            <span class="text-gray-500 dark:text-neutral-500">la</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">quotazione</span>
                                            <span class="text-gray-500 dark:text-neutral-500">con protocollo</span>
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <line x1="10" y1="9" x2="8" y2="9"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    P2025010025
                                                </span>
                                            </a>                                            
                                        </span>

                                        <!-- List Group Polizza -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Prodotto -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
                                                            <rect width="20" height="14" x="2" y="6" rx="2"/>
                                                        </svg>
                                                    </span>
                                                    Prodotto
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Moto Protezione Premium
                                                </span>
                                            </div>
                                            <!-- End Item -->                                            

                                            <!-- Item Veicolo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                                        </svg>                                                        
                                                    </span>
                                                    Veicolo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Yamaha MT-09
                                                </span>
                                            </div>
                                            <!-- End Item -->                                           

                                            <!-- Item Cliente (Link) -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Cliente
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <a href="#" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline">
                                                    Mario Rossi
                                                </a>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Stato -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                                            <path d="m9 11 3 3L22 4"/>
                                                        </svg>
                                                    </span>
                                                    Stato
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="inline-flex items-center gap-x-1.5 py-1 px-2 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">
                                                    <span class="size-1.5 inline-block rounded-full bg-yellow-800 dark:bg-yellow-500"></span>
                                                    Quotazione in corso
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        25 Settembre 2025 ore 09.42
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item - Inserimento Quotazione -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <line x1="10" y1="9" x2="8" y2="9"/>
                                    </svg>
                                </span>
                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Leonardo Furlan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">inserito</span>
                                            <span class="text-gray-500 dark:text-neutral-500">l'<span class="font-medium text-gray-800 dark:text-neutral-200">anagrafica</span></span>                                            
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="12" cy="7" r="4"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    Mario Rossi
                                                </span>
                                            </a>                                        
                                        </span>

                                        <!-- List Group Anagrafica -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Nome Completo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Nome
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Mario Rossi
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Data di Nascita -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8 2v4"/>
                                                            <path d="M16 2v4"/>
                                                            <rect width="18" height="18" x="3" y="4" rx="2"/>
                                                            <path d="M3 10h18"/>
                                                        </svg>
                                                    </span>
                                                    Data nascita
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    15/03/1985
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Codice Fiscale -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                    </span>
                                                    Codice Fiscale
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    ****************
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Indirizzo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                                                            <circle cx="12" cy="10" r="3"/>
                                                        </svg>
                                                    </span>
                                                    Indirizzo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Via Roma 123, Milano
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Telefono -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                                        </svg>
                                                    </span>
                                                    Telefono
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    +39 320 1234567
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Email -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <rect width="20" height="16" x="2" y="4" rx="2"/>
                                                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/>
                                                        </svg>
                                                    </span>
                                                    Email
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    <EMAIL>
                                                </span>
                                            </div>
                                            <!-- End Item -->
                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->                                               

                    </div>
                    <!-- End Vertical Steps -->
                </div>
                <!-- End Timeline -->


            </div>
            <!-- End Projects Card -->

        </div>
        <!-- End Content -->
    </div>
    <!-- End Grid -->
</div>
<!-- End Overview -->