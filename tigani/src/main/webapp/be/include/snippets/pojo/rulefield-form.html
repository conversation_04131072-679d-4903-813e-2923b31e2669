<!-- RuleField Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_RULEFIELD', '{{ routes("BE_RULEFIELD") }}');
    addRoute('BE_RULEFIELD_SAVE', '{{ routes("BE_RULEFIELD_SAVE") }}');
    addRoute('BE_RULEFIELD_OPERATE', '{{ routes("BE_RULEFIELD_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="mt-3">
            {% set postUrl = routes('BE_RULEFIELD_SAVE') %}
            {% if curRuleField.id is not empty %}
            {% set postUrl = routes('BE_RULEFIELD_SAVE') + '?ruleFieldId=' + curRuleField.id %}
            {% endif %}

            <form id="rulefield-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery mt-5">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curRuleField.id }}">

                <!-- Title -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Titolo: <span class="text-red-500">*</span>
                    </label>
                    <input name="title" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Titolo del campo" value="{{ curRuleField.title }}" required maxlength="100" {% if not user.hasPermission('RULEFIELD_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Type -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Tipo: <span class="text-red-500">*</span>
                    </label>
                    <select id="type" name="type" data-hs-select='{
                        "placeholder": "Seleziona tipologia campo...",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden" required {% if not user.hasPermission('RULEFIELD_MANAGEMENT', 'edit') %}disabled{% endif %}>
                    <option value="">Seleziona tipologia campo</option>
                    <option value="number" {% if curRuleField is not empty and curRuleField.type is not empty and curRuleField.type equals 'number' %}selected{% endif %}>Numero</option>
                    <option value="string" {% if curRuleField is not empty and curRuleField.type is not empty and curRuleField.type equals 'string' %}selected{% endif %}>Testo</option>
                    <option value="boolean" {% if curRuleField is not empty and curRuleField.type is not empty and curRuleField.type equals 'boolean' %}selected{% endif %}>Booleano</option>
                    <option value="lookup" {% if curRuleField is not empty and curRuleField.type is not empty and curRuleField.type equals 'lookup' %}selected{% endif %}>Lookup</option>
                    </select>
                </div>

                <!-- ClassName -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Classe:
                    </label>
                    <input name="className" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Nome classe del pojo" value="{{ curRuleField.className }}" maxlength="100" {% if not user.hasPermission('RULEFIELD_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">Nome del pojo, da inserire solo se tipologia è Lookup.</p>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 pt-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curRuleField is empty %}
                <!-- New RuleField - Show Save Button -->
                {% if user.hasPermission('RULEFIELD_MANAGEMENT', 'create') %}
                <button type="submit" form="rulefield-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Campo
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare nuovi campi
                </div>
                {% endif %}
            {% else %}
                <!-- Edit RuleField - Show Update and Delete Buttons -->
                {% if user.hasPermission('RULEFIELD_MANAGEMENT', 'delete') %}
                <button type="button" data-rulefieldid="{{ curRuleField.id }}" id="delete-rulefield-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('RULEFIELD_MANAGEMENT', 'edit') %}
                <button type="submit" form="rulefield-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Campo
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare questo campo
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>