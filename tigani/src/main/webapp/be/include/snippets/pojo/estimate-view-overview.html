<!-- Overview -->
<div class="xl:p-5 flex flex-col xl:bg-white xl:border xl:border-gray-200 xl:shadow-2xs xl:rounded-xl dark:xl:bg-neutral-800 dark:xl:border-neutral-700">
    <!-- Grid -->
    <div class="xl:flex">
        <!-- Activity Offcanvas -->
        <div id="hs-pro-dupsd" class="hs-overlay [--auto-close:xl]
             hs-overlay-open:translate-x-0
             -translate-x-full transition-all duration-300 transform
             w-80
             hidden
             fixed inset-y-0 start-0 z-60
             bg-white border-e border-gray-200
             xl:relative xl:z-0 xl:block xl:translate-x-0 xl:end-auto xl:bottom-0
             overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500
             dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1" aria-labelledby="hs-pro-dupsd-label">
            <div class="p-5 xl:p-0">
                <div class="xl:hidden">
                    <!-- Close Button -->
                    <div class="absolute top-3 end-3 z-10">
                        <button type="button" class="size-8 shrink-0 flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#hs-pro-dupsd">
                            <span class="sr-only">Close</span>
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18" />
                                <path d="m6 6 12 12" />
                            </svg>
                        </button>
                    </div>
                    <!-- End Close Button -->
                </div>

                <!-- Body -->
                <div class="xl:pe-4 mt-3 divide-y divide-gray-200 dark:divide-neutral-700">
                    <div class="py-4 first:pt-0 last:pb-0">
                        <h2 id="hs-pro-dupsd-label" class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                            Informazioni
                        </h2>

                        <ul class="mt-3 space-y-2">                                                        
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Sorgente:</span>
                                    <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                        <span class="w-1 h-3 bg-teal-500 rounded-full"></span>
                                        <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">
                                            Area dealer
                                        </span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Prodotto:</span>
                                    <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                        <img src="/tigani/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                        <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">HD Assistenza</span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Versione prodotto:</span>
                                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">v1.1</span>
                                </div>
                            </li>                            
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Modello:</span>
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">Harley Davidson Goldwing 900</span>
                                </div>
                            </li>
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Targa:</span>
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">FH115DS</span>
                                </div>
                            </li>
                            <li>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600 dark:text-neutral-400">Garanzie:</span>
                                    <span class="font-medium text-gray-800 dark:text-neutral-200 whitespace-normal text-end">RC, Tutela legale</span>
                                </div>
                            </li>                                                                              
                        </ul>
                        
                       
                    </div>
                    

                    <div class="py-4 first:pt-0 last:pb-0">
                        <h2 class="mb-3 text-sm font-semibold text-gray-800 dark:text-neutral-200">
                            Anagrafiche coinvolte
                        </h2>

                        <!-- List Group -->
                        <ul class="space-y-3">
                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <span class="flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 text-gray-700 text-xs font-medium uppercase rounded-full dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                        MM
                                    </span>
                                    <div class="grow">
                                        <div class="block text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            <a class="hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                                Mauro Milani
                                            </a>
                                            <span class="text-gray-500 dark:text-neutral-400"> (</span><a class="hover:text-blue-600 focus:outline-hidden focus:text-blue-700 dark:text-blue-500 dark:hover:text-blue-400 dark:focus:text-blue-400" href="../../pro/dashboard/dealer-profile.html">HD Bergamo</a><span class="text-gray-500 dark:text-neutral-400">)</span>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">
                                            Creatore
                                        </p>
                                    </div>                            
                                </div>
                            </li>
                            <!-- End List Item -->

                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <img class="shrink-0 size-9.5 rounded-full" src="https://images.unsplash.com/photo-1670272505340-d906d8d77d03?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2.5&w=320&h=320&q=80" alt="Avatar">
                                        <div class="grow">
                                            <a class="block text-sm font-medium text-gray-800 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                                Andrea Ballan
                                            </a>
                                            <p class="text-xs text-gray-500 dark:text-neutral-500">
                                                Contraente
                                            </p>
                                        </div>                          
                                </div>
                            </li>
                            <!-- End List Item -->

                            <!-- List Item -->
                            <li>
                                <div class="flex items-center gap-x-3">
                                    <span class="flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 text-gray-700 text-xs font-medium uppercase rounded-full dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                        MZ
                                    </span>
                                    <div class="grow">
                                        <a class="block text-sm font-medium text-gray-800 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-500 dark:focus:text-blue-500" href="../../pro/dashboard/user-profile.html">
                                            Marco Zen
                                        </a>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">
                                            Assicurato
                                        </p>
                                    </div>

                                </div>
                            </li>
                            <!-- End List Item -->


                        </ul>
                        <!-- End List Group -->
                    </div>

                </div>
                <!-- End Body -->
            </div>
        </div>
        <!-- End Activity Offcanvas -->

        <!-- Content -->
        <div class="xl:ps-5 grow space-y-5">
            <!-- Sales Card -->
            <div class="p-5 space-y-3 flex flex-col bg-white border border-gray-200 rounded-xl shadow-2xs xl:shadow-none dark:bg-neutral-800 dark:border-neutral-700">
                <!-- Header -->
                <div class="flex flex-wrap justify-between items-center gap-2">
                    <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                        Lifecycle
                    </h2>                    
                </div>
                <!-- End Header -->

                <!-- Empty State -->
                <div class="p-5  flex flex-col justify-center items-center text-center">
                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/>
                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/>
                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <g filter="url(#filter19)">
                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/>
                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/>
                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/>
                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                        </g>
                        <defs>
                            <filter id="filter19" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="6"/>
                                <feGaussianBlur stdDeviation="6"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/>
                            </filter>
                        </defs>
                    </svg>

                    <div class="max-w-sm mx-auto">
                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                            Quotazione in corso
                        </p>
                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                            Questa trattativa è ancora in fase di quotazione allo <b>step 2/4</b>.
                        </p>
                    </div>
                    <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-blue-500">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 9.003a1 1 0 0 1 1.517-.859l4.997 2.997a1 1 0 0 1 0 1.718l-4.997 2.997A1 1 0 0 1 9 14.996z"></path><circle cx="12" cy="12" r="10"></circle></svg>
                        Riprendi quotazione
                    </button>
                    <p class="mt-1.5 text-xs text-gray-500 dark:text-neutral-500">
                        Quotazione valida fino al 21/09/2025
                    </p>
                </div>
                <!-- End Empty State -->

            </div>
            <!-- End Sales Card -->

            <!-- Products Card -->
            <div class="flex flex-col bg-white border border-gray-200 rounded-xl shadow-2xs xl:shadow-none dark:bg-neutral-800 dark:border-neutral-700">
                <!-- Header -->
                <div class="p-5 pb-2 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <h2 class="inline-block font-semibold text-lg text-gray-800 dark:text-neutral-200">
                        Attività recente
                    </h2>                  
                    <!-- Form Group -->
                    <div class="flex sm:justify-end items-center gap-x-2">
                        <!-- Button -->
                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"></rect><path d="m3 17 2 2 4-4"></path><path d="M13 6h8"></path><path d="M13 12h8"></path><path d="M13 18h8"></path></svg>
                            Vedi tutto
                        </button>   
                        <!-- End Button -->
                    </div>
                    <!-- End Form Group -->
                </div>
                <!-- End Header -->

                <!-- Timeline -->
                <div class="px-5 max-h-100 overflow-y-auto
                     [&::-webkit-scrollbar]:w-2
                     [&::-webkit-scrollbar-track]:rounded-full
                     [&::-webkit-scrollbar-track]:bg-gray-100
                     [&::-webkit-scrollbar-thumb]:rounded-full
                     [&::-webkit-scrollbar-thumb]:bg-gray-300
                     dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                     dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                    <!-- Vertical Steps -->
                    <div class="pb-5">
                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        27 Settembre 2025 ore 10.43
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"/><path d="m3 17 2 2 4-4"/><path d="M13 6h8"/><path d="M13 12h8"/><path d="M13 18h8"/></svg>
                                </span>

                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Andrea Ballan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">iniziato</span>
                                            <span class="text-gray-500 dark:text-neutral-500">la</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">quotazione</span>
                                            <span class="text-gray-500 dark:text-neutral-500">con protocollo</span>
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <line x1="10" y1="9" x2="8" y2="9"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    P2025010025
                                                </span>
                                            </a>
                                        </span>

                                        <!-- List Group Polizza -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Prodotto -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
                                                            <rect width="20" height="14" x="2" y="6" rx="2"/>
                                                        </svg>
                                                    </span>
                                                    Prodotto
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Moto Protezione Premium
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Premio -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="12" cy="12" r="10"/>
                                                            <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"/>
                                                            <path d="M12 18V6"/>
                                                        </svg>
                                                    </span>
                                                    Premio
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    € 485,00
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Veicolo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                                        </svg>                                                        
                                                    </span>
                                                    Veicolo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Yamaha MT-09
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Scadenza -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8 2v4"/>
                                                            <path d="M16 2v4"/>
                                                            <rect width="18" height="18" x="3" y="4" rx="2"/>
                                                            <path d="M3 10h18"/>
                                                        </svg>
                                                    </span>
                                                    Scadenza
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    15/10/2025
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Cliente (Link) -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Cliente
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <a href="#" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline">
                                                    Mario Rossi
                                                </a>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Stato -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                                            <path d="m9 11 3 3L22 4"/>
                                                        </svg>
                                                    </span>
                                                    Stato
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="inline-flex items-center gap-x-1.5 py-1 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">
                                                    <span class="size-1.5 inline-block rounded-full bg-green-800 dark:bg-green-500"></span>
                                                    Polizza emessa
                                                </span>
                                            </div>
                                            <!-- End Item -->
                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        26 Settembre 2025 ore 11.02
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"/><path d="m3 17 2 2 4-4"/><path d="M13 6h8"/><path d="M13 12h8"/><path d="M13 18h8"/></svg>
                                </span>

                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Andrea Ballan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">iniziato</span>
                                            <span class="text-gray-500 dark:text-neutral-500">la</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">quotazione</span>
                                            <span class="text-gray-500 dark:text-neutral-500">con protocollo</span>
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                    <polyline points="14,2 14,8 20,8"/>
                                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                                    <line x1="10" y1="9" x2="8" y2="9"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    P2025010025
                                                </span>
                                            </a>                                            
                                        </span>

                                        <!-- List Group Polizza -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Prodotto -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
                                                            <rect width="20" height="14" x="2" y="6" rx="2"/>
                                                        </svg>
                                                    </span>
                                                    Prodotto
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Moto Protezione Premium
                                                </span>
                                            </div>
                                            <!-- End Item -->                                            

                                            <!-- Item Veicolo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/>
                                                        </svg>                                                        
                                                    </span>
                                                    Veicolo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Yamaha MT-09
                                                </span>
                                            </div>
                                            <!-- End Item -->                                           

                                            <!-- Item Cliente (Link) -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Cliente
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <a href="#" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline">
                                                    Mario Rossi
                                                </a>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Stato -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                                            <path d="m9 11 3 3L22 4"/>
                                                        </svg>
                                                    </span>
                                                    Stato
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="inline-flex items-center gap-x-1.5 py-1 px-2 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">
                                                    <span class="size-1.5 inline-block rounded-full bg-yellow-800 dark:bg-yellow-500"></span>
                                                    Quotazione in corso
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item -->
                        <div class="relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600">
                            <div class="w-full flex gap-x-4">
                                <span class="relative z-1 flex shrink-0 justify-center items-center w-8">
                                    <span class="flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600"></span>
                                </span>

                                <div class="grow">
                                    <span class="text-xs text-gray-500 dark:text-neutral-500">
                                        25 Settembre 2025 ore 09.42
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->

                        <!-- Step Item - Inserimento Quotazione -->
                        <div class="relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700">
                            <div class="w-full flex gap-x-4">
                                <span class="flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500">
                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <line x1="10" y1="9" x2="8" y2="9"/>
                                    </svg>
                                </span>
                                <div class="grow mt-1">
                                    <!-- Content -->
                                    <div class="flex flex-col gap-y-3">
                                        <span class="text-sm flex flex-wrap items-center gap-1">
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">Leonardo Furlan</span>
                                            <span class="text-gray-500 dark:text-neutral-500">ha</span>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">inserito</span>
                                            <span class="text-gray-500 dark:text-neutral-500">l'<span class="font-medium text-gray-800 dark:text-neutral-200">anagrafica</span></span>                                            
                                            <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="12" cy="7" r="4"/>
                                                </svg>
                                                <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    Mario Rossi
                                                </span>
                                            </a>                                        
                                        </span>

                                        <!-- List Group Anagrafica -->
                                        <div class="flex flex-col gap-y-1">
                                            <!-- Item Nome Completo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="12" cy="7" r="4"/>
                                                        </svg>
                                                    </span>
                                                    Nome
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Mario Rossi
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Data di Nascita -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8 2v4"/>
                                                            <path d="M16 2v4"/>
                                                            <rect width="18" height="18" x="3" y="4" rx="2"/>
                                                            <path d="M3 10h18"/>
                                                        </svg>
                                                    </span>
                                                    Data nascita
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    15/03/1985
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Codice Fiscale -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                    </span>
                                                    Codice Fiscale
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    ****************
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Indirizzo -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/>
                                                            <circle cx="12" cy="10" r="3"/>
                                                        </svg>
                                                    </span>
                                                    Indirizzo
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Via Roma 123, Milano
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Telefono -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                                        </svg>
                                                    </span>
                                                    Telefono
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    +39 320 1234567
                                                </span>
                                            </div>
                                            <!-- End Item -->

                                            <!-- Item Email -->
                                            <div class="py-0.5 flex items-center gap-x-2">
                                                <span class="flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400">
                                                    <span class="size-5 flex shrink-0 justify-center items-center">
                                                        <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <rect width="20" height="16" x="2" y="4" rx="2"/>
                                                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/>
                                                        </svg>
                                                    </span>
                                                    Email
                                                </span>
                                                <svg class="shrink-0 size-3 text-gray-600 dark:text-neutral-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    <EMAIL>
                                                </span>
                                            </div>
                                            <!-- End Item -->
                                        </div>
                                        <!-- End List Group -->
                                    </div>
                                    <!-- End Content -->
                                </div>
                            </div>
                        </div>
                        <!-- End Step Item -->                                               

                    </div>
                    <!-- End Vertical Steps -->
                </div>
                <!-- End Timeline -->


            </div>
            <!-- End Projects Card -->

        </div>
        <!-- End Content -->
    </div>
    <!-- End Grid -->
</div>
<!-- End Overview -->