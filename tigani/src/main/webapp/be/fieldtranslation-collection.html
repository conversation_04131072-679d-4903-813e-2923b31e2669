{% extends "be/include/base.html" %}

{% set page = 'FIELDTRANSLATION' %}

{% block extrahead %}
<title>Traduzioni Campi</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/fieldtranslation-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_FIELDTRANSLATION', '{{ routes("BE_FIELDTRANSLATION") }}');
addRoute('BE_FIELDTRANSLATION_DATA', '{{ routes("BE_FIELDTRANSLATION_DATA") }}');
addRoute('BE_FIELDTRANSLATION_OPERATE', '{{ routes("BE_FIELDTRANSLATION_OPERATE") }}');
addRoute('BE_FIELDTRANSLATION_REGENERATE', '{{ routes("BE_FIELDTRANSLATION_REGENERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Traduzioni Campi</h5>
            <div class="ms-sm-auto my-sm-auto">
                <button type="button" class="btn btn-success me-2" id="regenerate-translations">
                    <i class="ph-sparkle me-2"></i>
                    Rigenera con AI
                </button>
                <a href="{{ routes('BE_FIELDTRANSLATION') }}" class="btn btn-primary">
                    <i class="ph-plus me-2"></i>
                    Aggiungi Traduzione
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <i class="ph-info me-2"></i>
                    <div>
                        <strong>Gestione Traduzioni Campi</strong><br>
                        Qui puoi gestire le traduzioni dei campi dei POJO utilizzate nei log delle modifiche. 
                        Le traduzioni possono essere generate automaticamente tramite AI o modificate manualmente.
                    </div>
                </div>
            </div>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Selez.</th>
                    <th>Campo</th>
                    <th>Traduzione</th>
                    <th>Classe</th>
                    <th>Tipo</th>
                    <th>Base POJO</th>
                    <th>Origine</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->

</div>
<!-- /content area -->

{% endblock %}
