{% extends "be/include/base.html" %}

{% set page = 'CHANNEL' %}

{% block extrahead %}
<title>Canali</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/channel-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_CHANNEL_DATA', '{{ routes("BE_CHANNEL_DATA") }}');
addRoute('BE_CHANNEL_OPERATE', '{{ routes("BE_CHANNEL_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Canali</h5>
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_CHANNEL') }}" class="btn btn-primary">
                    <i class="ph-plus me-2"></i>
                    Aggiungi Canale
                </a>
            </div>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Selez.</th>
                    <th>Codice</th>
                    <th>Limite Capacità</th>
                    <th>Brand Models</th>
                    <th>Garanzie</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->

</div>
<!-- /content area -->



{% endblock %}
