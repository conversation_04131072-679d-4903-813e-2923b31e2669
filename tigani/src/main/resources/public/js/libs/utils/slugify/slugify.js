// Utility function to 'slugify' the text
function slugify(text) {
    return text
        .toString()                             // Converti in stringa
        .normalize('NFD')                       // Normalizza la stringa con decomposizione canonica
        .replace(/[\u0300-\u036f]/g, '')        // Rimuovi i segni diacritici (accenti)
        .toLowerCase()                          // Converte in minuscolo
        .trim()                                 // Rimuovi spazi bianchi all'inizio e alla fine
        .replace(/\s+/g, '-')                   // Sostituisci gli spazi con trattini
        .replace(/[^\w\-]+/g, '')               // Rimuovi caratteri non alfanumerici e trattini
        .replace(/\-\-+/g, '-');                // Sostituisci più trattini con un singolo trattino
}

// Utility function to update the 'identifierPreview'
function updateIdentifierPreview(slug) {
    var identifierPreview = document.getElementById('identifier-preview');
    if (identifierPreview) {
        identifierPreview.innerText = slug;
    }
}


function updateIdentifier(elementId) {
    // Event listener for changes in the 'title' field
    if ($("#title").length > 0) {
        document.getElementById('title').addEventListener('input', function () {
            if (!elementId) {
                // Only update the 'identifier' if it is currently empty
                var identifierInput = document.getElementById('identifier');
                var slug = slugify(this.value);
                identifierInput.value = slug;
                updateIdentifierPreview(slug); // Update preview directly with slug
            }
        });

        // Event listener for changes in the 'identifier' field
        document.getElementById('identifier').addEventListener('input', function () {
            updateIdentifierPreview(this.value); // Directly use the new value for preview
        });
    }
}
