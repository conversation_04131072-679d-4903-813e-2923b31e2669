/*!
 * FilePondPluginFilePoster 2.5.1
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */
.filepond--file-poster-wrapper {
  z-index: 2;
}

.filepond--image-preview-wrapper ~ .filepond--file-poster-wrapper {
  display: none;
}
.filepond--file-poster-overlay {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 5rem;
  max-height: 7rem;
  margin: 0;
  opacity: 0;
  z-index: 1;
  mix-blend-mode: multiply;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* fixes chrome bug */
  aspect-ratio: auto;
}

/* success (is second canvas) */
.filepond--file-poster-overlay:nth-of-type(2) {
  mix-blend-mode: normal;
}

/* error (is third canvas) */
.filepond--file-poster-overlay:nth-of-type(3) {
  mix-blend-mode: normal;
}

/* disable for <PERSON><PERSON> as mix-blend-mode causes the overflow:hidden of the parent container to not work */
@supports (-webkit-marquee-repetition: infinite) and
  ((-o-object-fit: fill) or (object-fit: fill)) {
  .filepond--file-poster-overlay {
    mix-blend-mode: normal;
  }
}
.filepond--file-poster-wrapper {
  /* no interaction */
  pointer-events: none;

  /* have preview fill up all available space */
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  margin: 0;

  /* radius is .05em less to prevent the panel background color from shining through */
  border-radius: 0.45em;
  overflow: hidden;

  /* this seems to prevent Chrome from redrawing this layer constantly */
  background: rgba(0, 0, 0, 0.01);
}
.filepond--file-poster {
  position: relative;
  z-index: 1;
  display: block;
  width: 100%;
  height: 100%;
  pointer-events: none;
  -webkit-transform-origin: center center;
  transform-origin: center center;
  background: #222;

  /* will be animated */
  will-change: transform, opacity;
}
.filepond--file-poster img {
  height: 100%;
  width: auto;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  display: block;
  will-change: transform;
}
.filepond--root[data-style-panel-layout~='integrated']
  .filepond--file-poster-wrapper {
  border-radius: 0;
}
.filepond--root[data-style-panel-layout~='integrated'] .filepond--file-poster {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--file-poster-wrapper {
  border-radius: 99999rem;
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--file-poster-overlay {
  top: auto;
  bottom: 0;
  -webkit-transform: scaleY(-1);
  transform: scaleY(-1);
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--file
  .filepond--file-action-button[data-align*='bottom']:not([data-align*='center']) {
  margin-bottom: 0.325em;
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--file
  [data-align*='left'] {
  left: calc(50% - 3em);
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--file
  [data-align*='right'] {
  right: calc(50% - 3em);
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--progress-indicator[data-align*='bottom'][data-align*='left'],
.filepond--root[data-style-panel-layout~='circle']
  .filepond--progress-indicator[data-align*='bottom'][data-align*='right'] {
  margin-bottom: calc(0.325em + 0.1875em);
}
.filepond--root[data-style-panel-layout~='circle']
  .filepond--progress-indicator[data-align*='bottom'][data-align*='center'] {
  margin-top: 0;
  margin-bottom: 0.1875em;
  margin-left: 0.1875em;
}