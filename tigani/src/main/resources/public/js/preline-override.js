// HSSelect - override non invasivo di loadMore per preservare la query di ricerca
(function overrideHsSelectLoadMore() {
function patch() {
    if (!window.HSSelect || window.HSSelect.prototype.__patchedLoadMoreWithSearch) return;

    // Override: loadMore con query + search
    window.HSSelect.prototype.loadMore = async function () {
        // debug check variabili
        console.log('loadMore apiUrl:', this.apiUrl);
        console.log('loadMore isLoading:', this.isLoading);
        console.log('loadMore hasMore:', this.hasMore);
        console.log('loadMore apiLoadMore:', this.apiLoadMore);
        if (!this.apiUrl || this.isLoading || !this.hasMore || !this.apiLoadMore) return;
        this.isLoading = true;

        try {
            // Costruisci URL partendo da apiUrl
            const url = new URL(this.apiUrl, window.location.origin);

            // (1) Re-inietta eventuali parametri statici da apiQuery
            if (this.apiQuery) {
                const base = new URLSearchParams(this.apiQuery);
                base.forEach((v, k) => url.searchParams.set(k, v));
            }

            // (2) Paginazione identica all’originale
            const pageKey =
                (this.apiFieldsMap && (this.apiFieldsMap.page || this.apiFieldsMap.offset)) || 'page';
            const isOffset = !!(this.apiFieldsMap && this.apiFieldsMap.offset);
            const perPage =
                (typeof this.apiLoadMore === 'object' ? this.apiLoadMore.perPage : 10) || 10;

            // Fix paginazione: per la prima pagina, currentPage è 0, ma l'url deve partire da 1
            if (this.currentPage === 0) {
                this.currentPage = 1;
            }

            if (isOffset) {
                const offset = this.currentPage * perPage;
                url.searchParams.set(pageKey, String(offset));
                this.currentPage++;
            } else {
                this.currentPage++;
                url.searchParams.set(pageKey, String(this.currentPage));
            }

            const limitKey = (this.apiFieldsMap && this.apiFieldsMap.limit) || 'limit';
            url.searchParams.set(limitKey, String(perPage));

            // (3) Aggiungi il termine di ricerca corrente (se presente)
            const searchKey = this.apiSearchQueryKey || 'q';
            const termFromInput =
                (this.search && typeof this.search.value === 'string')
                    ? this.search.value.trim()
                    : '';
            const term =
                termFromInput ||
                (typeof this.lastSearchTerm === 'string' ? this.lastSearchTerm.trim() : '');

            // Rispetta eventualmente minSearchLength (la classe usa "minSearchLength")
            const minLen = (typeof this.minSearchLength === 'number') ? this.minSearchLength : 0;
            if (term && term.length >= minLen) {
                url.searchParams.set(searchKey, term.toLowerCase());
            }

            // (4) Fetch + merge risultati (come originale)
            const res = await fetch(url.toString(), this.apiOptions || {});
            const json = await res.json();
            const data = this.apiDataPart ? json[this.apiDataPart] : json.results;
            const totalCount = json.count || 0;
            const consumed = this.currentPage * perPage;

            if (data && data.length > 0) {
                this.remoteOptions = [...(this.remoteOptions || []), ...data];
                this.buildOptionsFromRemoteData(data);
                this.hasMore = totalCount ? (consumed < totalCount) : (data.length === perPage);
            } else {
                this.hasMore = false;
            }
        } catch (err) {
            this.hasMore = false;
            console.error('HSSelect loadMore override error:', err);
        } finally {
            this.isLoading = false;
        }
    };

    window.HSSelect.prototype.__patchedLoadMoreWithSearch = true;

    // Facoltativo: intercetta la digitazione per avere un fallback `lastSearchTerm`
    const _remoteSearch = window.HSSelect.prototype.remoteSearch;
    if (_remoteSearch) {
        window.HSSelect.prototype.remoteSearch = async function (term) {
            this.lastSearchTerm = (typeof term === 'string') ? term : '';
            return _remoteSearch.apply(this, arguments);
        };
    }
}

// Applica la patch subito se la libreria è già caricata, altrimenti al "load"
if (window.HSSelect) patch();
else window.addEventListener('load', patch);
})();

(function resetPagingOnNewSearch() {
    function patch() {
        if (!window.HSSelect || window.HSSelect.prototype.__patchedResetOnSearch) return;

        const _remoteSearch = window.HSSelect.prototype.remoteSearch;

        window.HSSelect.prototype.remoteSearch = async function(term) {
            // 🔄 Reset stato di paginazione PRIMA di chiamare la fetch per il nuovo termine
            this.currentPage = 0;     // riparti dalla prima pagina
            this.hasMore = true;      // consenti di nuovo il loadMore
            this.isLoading = false;   // assicura che non resti bloccato in "loading"
            this.remoteOptions = [];  // azzera cache locale dei risultati
            if (this.dropdown) this.dropdown.scrollTop = 0; // rimetti la lista in cima

            // (facoltativo) conserva l’ultimo termine per loadMore
            this.lastSearchTerm = (typeof term === 'string') ? term : '';

            return _remoteSearch.apply(this, arguments);
        };

        window.HSSelect.prototype.__patchedResetOnSearch = true;
    }

    if (window.HSSelect) patch();
    else window.addEventListener('load', patch);
})();