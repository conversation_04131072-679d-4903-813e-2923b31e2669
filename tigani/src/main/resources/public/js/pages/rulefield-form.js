const RuleFieldForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitRuleField();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for rulefield
        /*$.validator.addMethod('ruleFieldTitle', function (value) {
            return /^[a-zA-Z0-9\s\-_]+$/.test(value);
        }, 'Il titolo può contenere solo lettere, numeri, spazi, trattini e underscore.');*/

        // Define custom rules for this form
        const customRules = {
            title: {
                required: true,
                ruleFieldTitle: true,
                maxlength: 100
            },
            type: {
                required: true,
                maxlength: 50
            },
            className: {
                maxlength: 100
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-rulefield-btn',
            permissionCheck: 'RULEFIELD_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-ruleFieldId',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo campo? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(ruleFieldId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('ruleFieldIds', ruleFieldId);
                formData.append('operation', 'delete');

                // $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_RULEFIELD_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.rulefieldsDataTable && window.rulefieldsDataTable.dataTable) {
                            window.rulefieldsDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Campo eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Campo eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        // $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during rulefield deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('RULEFIELD_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#rulefield-edit input, #rulefield-edit textarea, #rulefield-edit select').prop('readonly', true);
            $('#rulefield-edit select').prop('disabled', true);
            $('#rulefield-edit-offcanvas input, #rulefield-edit-offcanvas textarea, #rulefield-edit-offcanvas select').prop('readonly', true);
            $('#rulefield-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#rulefield-edit, #rulefield-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new rulefield forms
        const isNewrulefield = !$('#rulefield-edit input[name="id"]').val() && !$('#rulefield-edit-offcanvas input[name="id"]').val();
        if (isNewrulefield && !hasPermission('RULEFIELD_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#rulefield-edit input, #rulefield-edit textarea, #rulefield-edit select').prop('disabled', true);
            $('#rulefield-edit-offcanvas input, #rulefield-edit-offcanvas textarea, #rulefield-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#rulefield-edit, #rulefield-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitRuleField = function () {
        var idForm = "rulefield-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewRuleField = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewRuleField) {
                    if (!hasPermission('RULEFIELD_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare regole.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('RULEFIELD_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare regole.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                // $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.rulefieldsDataTable && window.rulefieldsDataTable.dataTable) {
                                window.rulefieldsDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Campo salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Campo salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        // $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during rulefield save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Title field - trim whitespace
        $('input[name="title"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Type field - lowercase transformation and trim
        /*$('input[name="type"]').on('input', function() {
            this.value = this.value.toLowerCase().trim();
        });*/

        // ClassName field - trim whitespace
        $('input[name="className"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    RuleFieldForm.init();
});


