const Settings = function () {
    // Initialization of components
    const init = function () {        
        _componentFilePond();
        _componentValidate();
        _componentMaxlength();
    };        

    // FilePond using centralized factory
    const _componentFilePond = function () {
        // Custom configuration for company logo with specific aspect ratio and transparency preservation
        const pond = TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {
            // Override default settings for company logo
            stylePanelLayout: 'compact',
            stylePanelAspectRatio: '25:10',
            imageCropAspectRatio: '25:10',
            imageResizeTargetWidth: 250,
            imageResizeTargetHeight: 100,
            maxFileSize: '1MB',
            // Preserve PNG transparency
            imageTransformOutputMimeType: null, // Keep original format
            imageTransformOutputQuality: null, // Keep original quality for PNG
            imageTransformOutputStripImageHead: false, // Preserve metadata
            imageTransformCanvasBackgroundColor: null // Transparent background
        });

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId && pond) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + pageVariables.get("imageId").replace("[", "").replace("]", "");
            pond.addFile(image);
        }
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // custom url validation
        $.validator.addMethod('identifier', function (value) {
            return /^[a-z0-9-_]+$/.test(value);
        }, 'URL non valido. L\'identificatore deve contenere solo lettere minuscole, numeri, trattini e sottolineature.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    Settings.init();
    submitCompany();
});

function submitCompany() {
    // Form submission handling
    $('#company').submit(function (e) {
        if ($('#company').valid()) {
            e.preventDefault();
            const formData = new FormData(this);
            // Supponendo che tu voglia inviare solo il primo file croppato
            if (pond.getFiles().length > 0) {
                // Ottieni la stringa base64 del file croppato
                const fileToInsert = pond.getFiles()[0];
                const base64String = fileToInsert.getFileEncodeBase64String();
                const mimeType = fileToInsert.fileType;
                const blob = base64ToBlob(base64String, mimeType);
                const fileName = fileToInsert.filename;
                const file = new File([blob], fileName, {type: mimeType});

                // Aggiungi il file croppato al FormData
                formData.append('file', file);
            }

            // formData.append('language', "en");
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_SETTINGS_COMPANY");
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during article save/update', error);
                }
            });
        }
    });
}

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}