var table;
var baseAjaxLink;
const SettingsUserCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }


        // Visibility button default class
        $.fn.dataTable.Buttons.defaults.dom.button.className = 'btn';

        // Date format for sorting
        $.fn.dataTable.moment('DD/MM/YYYY HH:mm');

        // Setting datatable defaults
        $.extend($.fn.dataTable.defaults, {
            autoWidth: false,
            dom: '<"datatable-header justify-content-center flex-wrap"f<"ms-sm-auto"l><"ms-sm-3"B>><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {search: "<span class='me-3'>Cerca:</span> <div class='form-control-feedback form-control-feedback-end flex-fill'>_INPUT_<div class='form-control-feedback-icon'><i class='ph-magnifying-glass opacity-50'></i></div></div>", searchPlaceholder: "Digita per cercare...", lengthMenu: "<span class='me-3'>Mostra:</span> _MENU_", paginate: {first: "Primo", last: "Ultimo", next: "Successivo", previous: "Precedente"}, info: "Mostra da _START_ a _END_ di _TOTAL_ elementi", infoEmpty: "Mostra 0 a 0 di 0 elementi", infoFiltered: "(filtrati da _MAX_ elementi totali)", zeroRecords: "Nessun record corrispondente trovato", emptyTable: "Nessun dato disponibile nella tabella", loadingRecords: "Caricamento...", processing: "Elaborazione...", aria: {sortAscending: ": attiva per ordinare la colonna in ordine crescente", sortDescending: ": attiva per ordinare la colonna in ordine decrescente"}, buttons: {copyTitle: 'Aggiunto negli appunti', copySuccess: {_: '%d righe copiate', 1: '1 riga copiata'}, copy: "Copia", colvis: "Visibilità colonna"}, select: {rows: {_: "Hai selezionato %d righe", 0: "", 1: "Una riga selezionata"}}},
            lengthMenu: [[25, 100, 250, 500], [25, 100, 250, 500]] // Opzioni personalizzate per il menu a tendina
        });


        baseAjaxLink = appRoutes.get("BE_SETTINGS_USER_DATA");
        var tableOptions = {
            ajax: {
                url: baseAjaxLink,
                dataType: 'json',
                dataSrc: function (datas) {
                    return datas.data;
                }
            },
            // Set default sorting on the third-to-last column don't remove!
            order: [[1, 'desc']],
            // select check first column
            pageLength: 25, // Numero di elementi visualizzati per pagina di default            
            
            scrollCollapse: true,
            // last column [+] button on small devices
            responsive: {
                details: {
                    type: 'column',
                    target: -1
                }
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'control',
                    orderable: false
                }
            ]
        };

        // Table init
        table = $('.datatable').DataTable(tableOptions);

        // Adjust columns on window resize
        setTimeout(function () {
            $(window).on('resize', function () {
                table.columns.adjust();
            });
        }, 100);

    };

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
        }
    };
}();


// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    SettingsUserCollection.init();
});
