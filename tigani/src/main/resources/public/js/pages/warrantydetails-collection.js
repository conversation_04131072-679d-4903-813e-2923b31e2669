var table;
var baseAjaxLink, lastCallLanguageParam;
const WarrantyDetailsCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }

        // Used to calculate the default order based on last change
        var totalColumns = document.querySelector('.datatable thead tr:first-child').cells.length;

        // Visibility button default class
        $.fn.dataTable.Buttons.defaults.dom.button.className = 'btn';

        // Date format for sorting
        $.fn.dataTable.moment('DD/MM/YYYY');

        // Setting datatable defaults
        $.extend($.fn.dataTable.defaults, {
            autoWidth: false,
            dom: '<"datatable-header justify-content-center flex-wrap"f<"ms-sm-auto"l><"ms-sm-3"B>><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {
                search: "<span class='me-3'>Cerca:</span> <div class='form-control-feedback form-control-feedback-end flex-fill'>_INPUT_<div class='form-control-feedback-icon'><i class='ph-magnifying-glass opacity-50'></i></div></div>",
                searchPlaceholder: "Digita per cercare...",
                lengthMenu: "<span class='me-3'>Mostra:</span> _MENU_",
                paginate: {first: "Primo", last: "Ultimo", next: "Successivo", previous: "Precedente"},
                info: "Mostra da _START_ a _END_ di _TOTAL_ elementi",
                infoEmpty: "Mostra 0 a 0 di 0 elementi",
                infoFiltered: "(filtrati da _MAX_ elementi totali)",
                zeroRecords: "Nessun record corrispondente trovato",
                emptyTable: "Nessun dato disponibile nella tabella",
                loadingRecords: "Caricamento...",
                processing: "Elaborazione...",
                aria: {
                    sortAscending: ": attiva per ordinare la colonna in ordine crescente",
                    sortDescending: ": attiva per ordinare la colonna in ordine decrescente"
                },
                buttons: {
                    copyTitle: 'Aggiunto negli appunti',
                    copySuccess: {_: '%d righe copiate', 1: '1 riga copiata'},
                    copy: "Copia",
                    colvis: "Visibilità colonna"
                },
                select: {rows: {_: "Hai selezionato %d righe", 0: "", 1: "Una riga selezionata"}}
            },
            lengthMenu: [[25, 100, 250, 500], [25, 100, 250, 500]] // Opzioni personalizzate per il menu a tendina
        });


        baseAjaxLink = appRoutes.get("BE_WARRANTYDETAILS_DATA");
        var tableOptions = {
            ajax: {
                url: baseAjaxLink,
                dataType: 'json',
                dataSrc: function (datas) {
                    return datas.data;
                }
            },
            // Set default sorting on the third-to-last column don't remove!
            order: [[totalColumns - 2, 'desc']],
            // select check first column
            pageLength: 25, // Numero di elementi visualizzati per pagina di default
            scrollCollapse: true,
            select: {
                style: 'multi',
                selector: 'td:first-child'
            },
            // last column [+] button on small devices
            responsive: {
                details: {
                    type: 'column',
                    target: -1
                }
            },
            // export and columns visibility
            buttons: {
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Azioni',
                        className: 'btn btn-light dropdown-toggle actions-button',
                        enabled: false,
                        buttons: [
                            {
                                text: '<i class="ph-trash me-2"></i> Archivia',
                                action: function (e, dt, node, config) {
                                    _archiveSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-x me-2"></i> Elimina',
                                action: function (e, dt, node, config) {
                                    _deleteSelectedRows();
                                }
                            }
                        ]
                    },
                    {
                        extend: 'excelHtml5',
                        title: document.title,
                        className: 'btn btn-light',
                        orientation: 'landscape',
                        pageSize: 'A4',
                        exportOptions: {
                            columns: ':visible:not(.select-checkbox)'
                        }
                    },
                    {
                        extend: 'pdfHtml5',
                        title: document.title,
                        className: 'btn btn-light',
                        exportOptions: {
                            columns: ':visible:not(.select-checkbox)'
                        },
                        customize: function (doc) {
                            doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                            doc.pageMargins = [10, 10, 10, 10];
                        }
                    },
                    {
                        extend: 'colvis',
                        text: '<i class="ph-list"></i>',
                        className: 'btn btn-light btn-icon dropdown-toggle'
                    }
                ]
            },
            // column definitions
            columnDefs: [
                // checkbox select don't remove!
                {
                    targets: 0,
                    width: 100,
                    orderable: false,
                    className: 'select-checkbox'
                },
                // actions button don't remove!
                {
                    targets: -2,
                    orderable: false,
                    className: 'text-center',
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                },
                // responsive button don't remove!
                {
                    targets: -1,
                    className: 'control',
                    orderable: false
                }
            ]
        };

        // Table init
        table = $('.datatable').DataTable(tableOptions);

        // Dopo la configurazione del DataTable
        table.on('select.dt deselect.dt', function () {
            var count = table.rows({selected: true}).count();
            var button = table.button('.actions-button');
            button.enable(count > 0);

            if (count > 0) {
                $(button.node()).removeClass('btn-light');
                $(button.node()).addClass('btn-primary');
            } else {
                $(button.node()).removeClass('btn-primary');
                $(button.node()).addClass('btn-light');
            }
        });

        // Adjust columns on window resize
        setTimeout(function () {
            $(window).on('resize', function () {
                table.columns.adjust();
            });
        }, 100);
    };

    function _renderActionDropdown(row) {
        // Extract warranty details ID from the second column
        var warrantyDetailsElement = $(row[1]);
        var warrantyDetailsId = warrantyDetailsElement.attr('warrantydetailsid');

        if (!warrantyDetailsId) {
            return '<span class="text-muted">N/A</span>';
        }

        var dropdownId = 'actionDropdown_' + warrantyDetailsId;

        var dropdown = '<div class="dropdown">' +
            '<button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="' + dropdownId + '" data-bs-toggle="dropdown" aria-expanded="false">' +
            '<i class="ph-gear me-1"></i>' +
            '</button>' +
            '<ul class="dropdown-menu" aria-labelledby="' + dropdownId + '">';

        dropdown += '<li><a class="dropdown-item" href="#" onclick="_archiveSingleRow(\'' + warrantyDetailsId + '\'); return false;">' +
            '<i class="ph-trash me-2"></i>Archivia</a></li>';

        dropdown += '<li><a class="dropdown-item text-danger" href="#" onclick="_deleteSingleRow(\'' + warrantyDetailsId + '\'); return false;">' +
            '<i class="ph-x me-2"></i>Elimina</a></li>';

        dropdown += '</ul></div>';

        return dropdown;
    }

    function _reloadTable(languages) {
        var newLink = baseAjaxLink;
        if (typeof languages !== 'undefined' && languages) {
            if (languages.includes("|")) {
                newLink += "?languages=" + languages;
            } else {
                newLink += "?language=" + languages;
            }
        } else {
            newLink += "?languages=" + $("#allLanguagesElement").attr("languages");
        }
        var isArchivedChecked = $("#warrantydetails_archived:checked").length > 0;
        if (isArchivedChecked) {
            if (!newLink.includes("?")) {
                newLink += "?archived=" + isArchivedChecked;
            } else {
                newLink += "&archived=" + isArchivedChecked;
            }
        }
        table.ajax.url(newLink).load();
    }

    function _archiveSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) {
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var warrantyDetailsIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (warrantyDetailsIds !== "") {
                            warrantyDetailsIds += ",";
                        }
                        warrantyDetailsIds += domElement.attr("warrantydetailsid");
                    }
                });

                const formData = new FormData();
                formData.append('warrantyDetailsIds', warrantyDetailsIds);
                formData.append('operation', "archive");
                formData.append('fromArchived', $("#warrantydetails_archived:checked").length > 0);
                if (warrantyDetailsIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_WARRANTYDETAILS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during warranty details archive', error);
                        }
                    });
                }
            }
        }
    }

    function _deleteSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) {
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var warrantyDetailsIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (warrantyDetailsIds !== "") {
                            warrantyDetailsIds += ",";
                        }
                        warrantyDetailsIds += domElement.attr("warrantydetailsid");
                    }
                });

                const formData = new FormData();
                formData.append('warrantyDetailsIds', warrantyDetailsIds);
                formData.append('operation', "delete");
                formData.append('fromArchived', $("#warrantydetails_archived:checked").length > 0);
                if (warrantyDetailsIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_WARRANTYDETAILS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during warranty details delete', error);
                        }
                    });
                }
            }
        }
    }

    function _archiveSingleRow(warrantyDetailsId) {
        _performSingleRowAction(warrantyDetailsId, 'archive');
    }

    function _deleteSingleRow(warrantyDetailsId) {
        if (confirm('Sei sicuro di voler eliminare questo dettaglio garanzia? Questa azione non può essere annullata.')) {
            _performSingleRowAction(warrantyDetailsId, 'delete');
        }
    }

    function _performSingleRowAction(warrantyDetailsId, operation) {
        const formData = new FormData();
        formData.append('warrantyDetailsIds', warrantyDetailsId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#warrantydetails_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_WARRANTYDETAILS_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                table.ajax.reload();
                new Noty({
                    text: 'Operazione completata correttamente.',
                    type: 'success',
                    closeWith: ['button']
                }).show();
            },
            error: function (error) {
                new Noty({
                    text: error.responseText || 'Errore durante l\'operazione',
                    type: 'error',
                    closeWith: ['button']
                }).show();
                console.error('Error during warranty details operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;

    return {
        init: function () {
            _componentDatatable();
        },
        reloadTable: _reloadTable,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows
    };
}();

document.addEventListener('DOMContentLoaded', function () {
    WarrantyDetailsCollection.init();
});
