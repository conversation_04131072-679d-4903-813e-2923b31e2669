document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const mobileSelect = document.getElementById('category-select');
    const cardsGrid = document.getElementById('cards-grid');
    // Seleziona solo le card nel grid, non i bottoni della sidebar
    const allCards = document.querySelectorAll('#cards-grid [data-category]');
    
    // Classi per stati attivo/inattivo
    const activeClasses = ['bg-gray-100', 'text-gray-800', 'dark:bg-neutral-700', 'dark:text-neutral-200'];
    const inactiveHoverClasses = ['hover:bg-gray-100', 'dark:hover:bg-neutral-700'];
    
    // Gestione click sui tab desktop
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            setActiveTab(category);
            filterCards(category);
            syncMobileSelect(category);
        });
    });
    
    // Gestione cambio select mobile
    mobileSelect.addEventListener('change', function() {
        const category = this.value;
        setActiveTab(category);
        filterCards(category);
    });
    
    // Imposta il tab attivo usando solo classi Tailwind
    function setActiveTab(activeCategory) {
        tabButtons.forEach(button => {
            if (button.dataset.category === activeCategory) {
                // Attiva tab
                button.classList.add(...activeClasses);
                button.classList.remove(...inactiveHoverClasses);
            } else {
                // Disattiva tab
                button.classList.remove(...activeClasses);
                button.classList.add(...inactiveHoverClasses);
            }
        });
    }
    
    // Sincronizza il select mobile
    function syncMobileSelect(category) {
        mobileSelect.value = category;
    }
    
    // Filtra le card (solo quelle nel grid, non i bottoni sidebar)
    function filterCards(category) {
        let visibleCount = 0;
        
        allCards.forEach(card => {
            const cardCategories = card.dataset.category.split(' ');
            const shouldShow = category === 'all' || cardCategories.includes(category);
            
            if (shouldShow) {
                card.classList.remove('hidden');
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.classList.add('hidden');
                card.style.display = 'none';
            }
        });
        
        toggleEmptyState(visibleCount === 0);
    }
    
    // Stato vuoto (con SVG originale)
    function toggleEmptyState(show) {
        let emptyState = document.getElementById('empty-state');
        
        if (show && !emptyState) {
            emptyState = document.createElement('div');
            emptyState.id = 'empty-state';
            emptyState.className = 'col-span-full';
            emptyState.innerHTML = `
                <div class="p-5 flex flex-col justify-center items-center text-center">
                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/>
                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/>
                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <g filter="url(#filter5)">
                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/>
                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/>
                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/>
                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                        </g>
                        <defs>
                            <filter id="filter5" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="6"/>
                                <feGaussianBlur stdDeviation="6"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/>
                            </filter>
                        </defs>
                    </svg>
                    <div class="max-w-sm mx-auto">
                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                            Nessuna tabella disponibile
                        </p>
                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                            Non hai i permessi per visualizzare le tabelle di questa categoria.
                        </p>
                    </div>
                </div>
            `;
            cardsGrid.appendChild(emptyState);
        }
        
        if (emptyState) {
            emptyState.classList.toggle('hidden', !show);
        }
    }
    
    // Inizializzazione - imposta il primo tab come attivo
    setActiveTab('all');
    filterCards('all');
});