// Global variables for table management
const ContactCollection = function () {

    // Datatable using centralized factory
    const _componentDatatable = function () {
        // Set starting date
        var params = [];
        var startDate = moment().subtract(29, 'days').format('DD/MM/YYYY');
        var endDate = moment().format('DD/MM/YYYY');
        if (startDate && startDate.trim() !== '') {
            params.push("startDate=" + encodeURIComponent(startDate));
        }
        if (endDate && endDate.trim() !== '') {
            params.push("endDate=" + encodeURIComponent(endDate));
        }

        // Custom configuration specific to contact collection
        const customConfig = {
            ajax: {
                url: appRoutes.get("BE_CONTACT_DATA") + (params.length > 0 ? '?' + params.join('&') : ''),
                type: 'GET'
            },
            columnDefs: [
                {
                    targets: 2,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            const typeMap = {
                                'PERSON': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-500">Persona</span>',
                                'COMPANY': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">Azienda</span>'
                            }
                            return typeMap[data] || `<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-500">${data}</span>`;
                        }
                        return data;
                    }
                },
                {
                    targets: 4,
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const statusMap = {
                                'ACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">Attivo</span>',
                                'INACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-500">Inattivo</span>',
                                'PENDING': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">In Attesa</span>',
                                'customer': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-500">Cliente</span>',
                                'admin': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800/30 dark:text-purple-500">Admin</span>'
                            };
                            return statusMap[data] || `<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-500">${data}</span>`;
                        }
                        return data;
                    }
                },
                {
                    targets: -1,
                    orderable: false,
                    className: 'w-0 text-center',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                }
            ]
        };

        // Create DataTable using centralized factory - base config is cloned and merged with custom options
        const hsDataTable = TiganiLibs.DataTableFactory.create('#contact-datatable-container', customConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.contactsDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Action dropdown renderer
    function _renderActionDropdown(row) {
        // Extract contact ID from the link in the second column (index 1)
        const linkHtml = row[1];
        const contactIdMatch = linkHtml.match(/contactId='([^']+)'/);
        const contactId = contactIdMatch ? contactIdMatch[1] : '';

        // Build action items based on permissions
        let actionItems = '';

        // Archive action - requires edit permission
        if (hasPermission('CONTACT_MANAGEMENT', 'edit')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_archiveSingleRow('${contactId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                    Archivia
                </a>`;
        }

        // Delete action - requires delete permission
        if (hasPermission('CONTACT_MANAGEMENT', 'delete')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-red-100 focus:outline-hidden focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30" href="#" onclick="_deleteSingleRow('${contactId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                    Elimina
                </a>`;
        }

        // If no actions are available, don't show the dropdown
        if (!actionItems.trim()) {
            return '<span class="text-sm text-gray-500 dark:text-neutral-400">Nessuna azione disponibile</span>';
        }

        return `
            <div class="hs-dropdown relative inline-flex">
                <button id="hs-table-dropdown-${contactId || Math.random()}" type="button" class="hs-dropdown-toggle py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    Azioni
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-neutral-700 dark:bg-neutral-800 dark:border dark:border-neutral-700" role="menu" aria-orientation="vertical">
                    <div class="py-2 first:pt-0 last:pb-0">
                        ${actionItems}
                    </div>
                </div>
            </div>
        `;
    }

    function _componentDatepicker() {
        var start = moment().subtract(29, 'days');
        var end = moment();

        function cb(start, end) {
            $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        }

        $('#reportrange').daterangepicker({
            startDate: start,
            endDate: end,
            opens: 'left',
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 Giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 Giorni': [moment().subtract(29, 'days'), moment()],
                'Questo Mese': [moment().startOf('month'), moment().endOf('month')],
                'Mese Scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            }
        }, cb);

        // Add apply event handler for date filtering
        $('#reportrange').on('apply.daterangepicker', function(ev, picker) {
            var startDate = picker.startDate.format('DD/MM/YYYY');
            var endDate = picker.endDate.format('DD/MM/YYYY');
            _reloadTableWithDateFilter(startDate, endDate);
        });

        cb(start, end);
    }

    function _componentAddContact() {
        // Create Contact Button Handler
        const createContactBtn = document.getElementById('create-contact-btn');
        if (createContactBtn) {
            createContactBtn.addEventListener('click', function() {
                try {
                    // Check if required functions are available
                    if (typeof createDynamicOffcanvas !== 'function') {
                        showToast('Errore: funzione offcanvas non disponibile', 'error');
                        return;
                    }

                    if (!appRoutes.has('BE_CONTACT_FORM')) {
                        showToast('Errore: route non configurata', 'error');
                        return;
                    }

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuovo Contatto',
                        url: appRoutes.get('BE_CONTACT_FORM'),
                        entity: 'contact',
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize contact form components after content is loaded
                                if (typeof ContactForm !== 'undefined' && ContactForm.init) {
                                    ContactForm.init();
                                    initAutocomplete({
                                        inputField: 'fullAddress',
                                        fields: {
                                            city: 'city',
                                            address: 'address',
                                            streetNumber: 'streetNumber',
                                            postalCode: 'postalCode',
                                            countryCode: 'countryCode',
                                            provinceCode: 'provinceCode'
                                        }
                                    });
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        },
                        onClose: function() {
                            // Clean up any global variables if needed
                            if (typeof pond !== 'undefined' && pond && pond.destroy) {
                                try {
                                    pond.destroy();
                                } catch (e) {
                                    console.warn('Error destroying FilePond:', e);
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create contact button not found');
        }
    }

    function _componentEditContact() {
        // Edit Contact Click Handler for table rows
        $(document).on('click', 'a[contactId]', function(e) {
            e.preventDefault();

            try {
                const contactId = $(this).attr('contactId');
                const contactName = $(this).text().trim();

                if (!contactId) {
                    showToast('Errore: ID contatto non trovato', 'error');
                    return;
                }

                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_CONTACT_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Contatto: ' + (contactName || 'Sconosciuto'),
                    url: appRoutes.get('BE_CONTACT_FORM') + '?contactId=' + encodeURIComponent(contactId),
                    entity: 'contact',
                    entityId: contactId,
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize contact form components after content is loaded
                            if (typeof ContactForm !== 'undefined' && ContactForm.init) {
                                ContactForm.init();
                                initAutocomplete({
                                    inputField: 'fullAddress',
                                    fields: {
                                        city: 'city',
                                        address: 'address',
                                        streetNumber: 'streetNumber',
                                        postalCode: 'postalCode',
                                        countryCode: 'countryCode',
                                        provinceCode: 'provinceCode'
                                    }
                                });
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    },
                    onClose: function() {
                        // Clean up any global variables if needed
                        if (typeof pond !== 'undefined' && pond && pond.destroy) {
                            try {
                                pond.destroy();
                            } catch (e) {
                                console.warn('Error destroying FilePond:', e);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        });
    }

    // Setup manual checkbox handlers for Preline UI compatibility
    function _setupCheckboxHandlers() {
        // Handle select-all checkbox
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', _handleSelectAll); // Remove existing listener
            selectAllCheckbox.addEventListener('change', _handleSelectAll);
        }

        // Handle individual row checkboxes
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        individualCheckboxes.forEach(checkbox => {
            checkbox.removeEventListener('change', _handleIndividualSelect); // Remove existing listener
            checkbox.addEventListener('change', _handleIndividualSelect);
        });

        // Update bulk action buttons state
        _updateBulkActionButtons();
    }

    function _handleSelectAll(event) {
        const isChecked = event.target.checked;
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');

        individualCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });

        _updateBulkActionButtons();
    }

    function _handleIndividualSelect() {
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === individualCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        _updateBulkActionButtons();
    }

    function _reloadTable(archived) {
        _reloadTableWithDateFilter(null, null, archived);
    }

    function _reloadTableWithDateFilter(startDate, endDate, archived) {
        var newLink = appRoutes.get("BE_CONTACT_DATA");
        var params = [];

        // check archiviati
        var isArchivedChecked = archived || $("#contact_archived:checked").length > 0;
        if (isArchivedChecked) {
            params.push("archived=" + isArchivedChecked);
        }

        // Add date filtering parameters
        if (startDate && startDate.trim() !== '') {
            params.push("startDate=" + encodeURIComponent(startDate));
        }
        if (endDate && endDate.trim() !== '') {
            params.push("endDate=" + encodeURIComponent(endDate));
        }

        // Build final URL
        if (params.length > 0) {
            newLink += "?" + params.join("&");
        }

        if (window.contactsDataTable && window.contactsDataTable.dataTable) {
            window.contactsDataTable.dataTable.ajax.url(newLink).load();
        }
    }

    function _archiveSelectedRows() {
        // Check permission first
        if (!hasPermission('CONTACT_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        const contactIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('contactIds', contactIds);
        formData.append('operation', "archive");
        formData.append('fromArchived', $("#contact_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_CONTACT_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati salvati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during contact archive', error);
            }
        });
    }

    function _deleteSelectedRows() {
        // Check permission first
        if (!hasPermission('CONTACT_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: `Sei sicuro di voler eliminare ${selectedRows.length} element${selectedRows.length > 1 ? 'i' : 'o'}? Questa azione non può essere annullata.`,
            theme: 'preline',
            type: 'red',
            typeAnimated: true,
            columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performDeleteSelectedRows(selectedRows);
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performDeleteSelectedRows(selectedRows) {

        const contactIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('contactIds', contactIds);
        formData.append('operation', "delete");
        formData.append('fromArchived', $("#contact_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_CONTACT_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati eliminati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                console.error('Error during contact delete', error);
            }
        });
    }

    // Helper functions for selection management
    function _getSelectedRows() {
        const selectedRows = [];
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row && window.contactsDataTable && window.contactsDataTable.dataTable) {
                const rowData = window.contactsDataTable.dataTable.row(row).data();
                if (rowData && rowData.length > 1) {
                    // Extract contact ID from the link in the second column (index 1)
                    const linkHtml = rowData[1];
                    const contactIdMatch = linkHtml.match(/contactId='([^']+)'/);
                    if (contactIdMatch && contactIdMatch[1]) {
                        selectedRows.push({ id: contactIdMatch[1], data: rowData });
                    }
                }
            }
        });

        return selectedRows;
    }

    function _clearSelection() {
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual], #hs-table-search-checkbox-all');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });
        _updateBulkActionButtons();
    }

    function _updateBulkActionButtons() {
        const selectedCount = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked').length;
        const bulkActionContainer = document.getElementById('bulk-actions-container');
        const selectedCountElement = document.getElementById('selected-count');

        if (bulkActionContainer && selectedCountElement) {
            if (selectedCount > 0) {
                // Show the enhanced bulk actions container
                bulkActionContainer.classList.remove('hidden');
                selectedCountElement.textContent = selectedCount;
            } else {
                // Hide the enhanced bulk actions container
                bulkActionContainer.classList.add('hidden');
            }
        }

        // Legacy support for old bulk action buttons (if any still exist)
        const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
        bulkActionButtons.forEach(button => {
            // Update button text with count
            const buttonTextSpan = button.querySelector('.bulk-action-text');
            if (buttonTextSpan) {
                if (selectedCount > 0) {
                    buttonTextSpan.textContent = `Azioni (${selectedCount})`;
                } else {
                    buttonTextSpan.textContent = 'Azioni';
                }
            }

            if (selectedCount > 0) {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }

    function _archiveSingleRow(contactId) {
        // Check permission first
        if (!hasPermission('CONTACT_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }
        _performSingleRowAction(contactId, 'archive');
    }

    function _deleteSingleRow(contactId) {
        // Check permission first
        if (!hasPermission('CONTACT_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: 'Sei sicuro di voler eliminare questo utente? Questa azione non può essere annullata.',
            theme: 'preline',
            type: 'red',
            typeAnimated: true,
            columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performSingleRowAction(contactId, 'delete');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performSingleRowAction(contactId, operation) {
        const formData = new FormData();
        formData.append('contactIds', contactId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#contact_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_CONTACT_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during contact operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
            _componentDatepicker();
            _componentAddContact();
            _componentEditContact();
        },
        reloadTable: _reloadTable,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows,
        getSelectedRows: _getSelectedRows,
        clearSelection: _clearSelection
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    ContactCollection.init();
});
