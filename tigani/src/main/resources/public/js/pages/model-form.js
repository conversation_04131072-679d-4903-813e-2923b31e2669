const ModelForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitModel();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for model
        $.validator.addMethod('modelCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

        // Define custom rules for this form
        const customRules = {
            codice: {
                required: true,
                modelCode: true,
                maxlength: 50
            },
            brandId: {
                required: true
            },
            descrizione: {
                required: true,
                maxlength: 100
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-model-btn',
            permissionCheck: 'MODEL_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-modelid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo modello? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(modelId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('modelIds', modelId);
                formData.append('operation', 'delete');

                // $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_MODEL_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.modelsDataTable && window.modelsDataTable.dataTable) {
                            window.modelsDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Marchio eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Marchio eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        // $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during model deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('MODEL_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#model-edit input, #model-edit textarea, #model-edit select').prop('readonly', true);
            $('#model-edit select').prop('disabled', true);
            $('#model-edit-offcanvas input, #model-edit-offcanvas textarea, #model-edit-offcanvas select').prop('readonly', true);
            $('#model-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#model-edit, #model-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new model forms
        const isNewModel = !$('#model-edit input[name="id"]').val() && !$('#model-edit-offcanvas input[name="id"]').val();
        if (isNewModel && !hasPermission('MODEL_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#model-edit input, #model-edit textarea, #model-edit select').prop('disabled', true);
            $('#model-edit-offcanvas input, #model-edit-offcanvas textarea, #model-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#model-edit, #model-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitModel = function () {
        var idForm = "model-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewModel = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewModel) {
                    if (!hasPermission('MODEL_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare modelli.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('MODEL_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare modelli.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                // $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.modelsDataTable && window.modelsDataTable.dataTable) {
                                window.modelsDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Marchio salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Marchio salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        // $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during model save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="codice"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Description field - trim whitespace
        $('input[name="descrizione"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    ModelForm.init();
});
