const WarrantyForm = function () {
    // Initialization of components
    const init = function (options) {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitWarranty(options);
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for warranty
        $.validator.addMethod('warrantyCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

        // Define custom rules for this form
        const customRules = {
            code: {
                required: true,
                warrantyCode: true,
                maxlength: 50
            },
            title: {
                required: true,
                maxlength: 100
            },
            warrantyTypeId: {
                required: true
            },
            insuranceCompanyId: {
                required: true
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-warranty-btn',
            permissionCheck: 'WARRANTY_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-warrantyid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questa garanzia? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(warrantyId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('warrantyIds', warrantyId);
                formData.append('operation', 'delete');

                // $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_WARRANTY_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.warrantiesDataTable && window.warrantiesDataTable.dataTable) {
                            window.warrantiesDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Garanzia eliminata correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Garanzia eliminata correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        // $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during warranty deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('WARRANTY_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#warranty-edit input, #warranty-edit textarea, #warranty-edit select').prop('readonly', true);
            $('#warranty-edit select').prop('disabled', true);
            $('#warranty-edit-offcanvas input, #warranty-edit-offcanvas textarea, #warranty-edit-offcanvas select').prop('readonly', true);
            $('#warranty-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#warranty-edit, #warranty-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new warranty forms
        const isNewWarranty = !$('#warranty-edit input[name="id"]').val() && !$('#warranty-edit-offcanvas input[name="id"]').val();
        if (isNewWarranty && !hasPermission('WARRANTY_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#warranty-edit input, #warranty-edit textarea, #warranty-edit select').prop('disabled', true);
            $('#warranty-edit-offcanvas input, #warranty-edit-offcanvas textarea, #warranty-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#warranty-edit, #warranty-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitWarranty = function (options) {
        var idForm = "warranty-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewWarranty = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewWarranty) {
                    if (!hasPermission('WARRANTY_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare garanzie.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('WARRANTY_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare garanzie.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                // $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.warrantiesDataTable && window.warrantiesDataTable.dataTable) {
                                window.warrantiesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            if (options && typeof options.callback === "function") {
                                options.callback(response);
                            }

                            // Show success message
                            showToast('Garanzia salvata correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Garanzia salvata, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        // $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during warranty save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="code"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Title field - trim whitespace
        $('input[name="title"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Description field - trim whitespace
        $('textarea[name="description"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    WarrantyForm.init();
});
