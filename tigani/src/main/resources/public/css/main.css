@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

/* Preline UI - Path assoluto */
@source "C:/projects/tigani/tigani/node_modules/preline/dist/*.js";
@import "C:/projects/tigani/tigani/node_modules/preline/variants.css";
@import "C:/projects/tigani/tigani/node_modules/preline/src/plugins/datepicker/styles.css";

/* Plugins */
@plugin "@tailwindcss/forms";

/* Adds pointer cursor to buttons */
@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

/* Defaults hover styles on all devices */
@custom-variant hover (&:hover);

/* Custom */
.dt-layout-row:has(.dt-search),
.dt-layout-row:has(.dt-length),
.dt-layout-row:has(.dt-paging) {
  display: none !important;
}