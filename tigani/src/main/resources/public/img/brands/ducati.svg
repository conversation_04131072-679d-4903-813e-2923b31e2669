<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="Livello_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 100 106.2"
	 style="enable-background:new 0 0 100 106.2;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#E10025;}
</style>
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  bottomLeftOrigin="true" height="106.2" width="100" x="30.6" y="32.5"></sliceSourceBounds>
	</sfw>
</metadata>
<path class="st0" d="M8.9,9.1C4.8,11,0,15.4,0,25c0,1.6,0.1,3.1,0.3,4.7l0.1,0.7c0.6,4.4,2.4,17.7,9.1,31.8s13,23.1,23.1,33.2
	c0.1,0.1,7.6,7.6,11.4,9.4c1.9,0.9,3.9,1.4,6,1.4l0,0c2.1,0,4.1-0.5,6-1.4c3.7-1.9,11.3-9.3,11.4-9.4c10.1-10.1,16.3-19.1,23.1-33.2
	s8.5-27.4,9.1-31.8l0.1-0.8C101.4,17,96,11.4,91.1,9.1C78.7,3.1,64.9,0,50,0S21.3,3.1,8.9,9.1"/>
<path class="st1" d="M68.1,46.1c-26.5,4.1-39.4,11.2-45.7,17.8c-4.8,5.1-3.1,9-1.9,11.2c4.2,6.5,9,12.5,14.6,17.9
	c1.9,1.9,7.9,7.4,10.4,8.8c2.8,1.4,6.1,1.4,8.9,0c2.6-1.3,8.6-6.9,10.4-8.8c9.8-9.8,15.9-18.5,22.4-32.2c2.6-5.5,4.6-11.2,6.1-17
	v-0.1C84.9,43.8,76.4,44.7,68.1,46.1 M19.4,21.9h-0.5l-1.5,7.4h0.5c0.7,0,1.4-0.3,1.9-0.9c0.6-0.9,0.9-1.8,1.1-2.8
	c0.3-1.3,0.4-2.3,0.1-2.8C20.7,22.1,20,21.8,19.4,21.9L19.4,21.9z M89.6,12.2C76.5,5.8,62.7,3.5,50,3.5s-26.5,2.3-39.6,8.7
	c-4.6,2.3-7,6.5-7,12.8c0,1.4,0.1,2.8,0.3,4.2l0.1,0.8c0.9,6.8,2.3,13.4,4.5,19.9c3.5-4.4,17.2-8.2,41.6-9.5
	c23-1.2,39.5-1.1,44.5-1.1c0.9-4.2,1.4-7.6,1.6-9.3l0.1-0.8C97.4,20.6,95.2,14.9,89.6,12.2z M26.3,25.7c-0.7,3.9-4.2,6.8-8.2,6.6
	h-6.7l2.9-13.5h6.4C26,18.8,27.2,21.7,26.3,25.7z M32.6,32.7c-4.1,0-6.1-1.6-5.4-4.9l1.9-9h5.4l-1.9,8.8c-0.2,1.1,0.2,1.5,0.8,1.5
	s1.2-0.4,1.4-1.5l1.9-8.8H42l-1.9,9C39.3,31.1,36.6,32.7,32.6,32.7z M47.3,32.7c-4.7,0-6.3-2.9-5.4-7.2c0.8-4.2,3.9-7.1,8.5-7.1
	c3.4,0,6,1.4,5.2,5.4h-4.9c0.3-1.5-0.2-1.9-0.9-1.9c-1.4,0-1.8,1.4-2.3,3.6c-0.4,2.1-0.6,3.6,0.7,3.6c0.7,0,1.2-0.4,1.6-1.9h5
	C53.9,31.1,50.6,32.7,47.3,32.7z M63.5,32.3l-0.1-2.2h-3.9l-1,2.2h-5.1l7.2-13.5h7l1.5,13.5H63.5z M76,32.3h-5.3l2.1-9.9h-3.3
	l0.8-3.6h12l-0.8,3.6h-3.3L76,32.3z M85.7,32.3h-5.4l2.9-13.5h5.4L85.7,32.3z M61,26.9h2.3v-5.1l0,0L61,26.9z"/>
</svg>
